using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// A simple, generic object pool for GameObjects
/// </summary>
[System.Serializable]
public class SimpleObjectPool
{
    [SerializeField] private GameObject prefab;
    [SerializeField] private int initialSize = 10;
    [SerializeField] private int maxSize = 50;
    [SerializeField] private bool expandable = true;
    
    private Queue<GameObject> pool = new Queue<GameObject>();
    private HashSet<GameObject> activeObjects = new HashSet<GameObject>();
    private Transform poolParent;
    
    public int PoolSize => pool.Count;
    public int ActiveCount => activeObjects.Count;
    public int TotalCount => pool.Count + activeObjects.Count;
    
    /// <summary>
    /// Initialize the object pool
    /// </summary>
    /// <param name="prefab">The prefab to pool</param>
    /// <param name="initialSize">Initial number of objects to create</param>
    /// <param name="maxSize">Maximum number of objects in the pool</param>
    /// <param name="parent">Parent transform for pooled objects</param>
    public void Initialize(GameObject prefab, int initialSize = 10, int maxSize = 50, Transform parent = null)
    {
        this.prefab = prefab;
        this.initialSize = initialSize;
        this.maxSize = maxSize;
        this.poolParent = parent;
        
        // Create initial pool
        for (int i = 0; i < initialSize; i++)
        {
            GameObject obj = Object.Instantiate(prefab, poolParent);
            obj.SetActive(false);
            pool.Enqueue(obj);
        }
        
        Debug.Log($"SimpleObjectPool: Initialized pool for {prefab.name} with {initialSize} objects");
    }
    
    /// <summary>
    /// Get an object from the pool
    /// </summary>
    /// <param name="position">Position to place the object</param>
    /// <param name="rotation">Rotation to apply to the object</param>
    /// <returns>The pooled object, or null if pool is at capacity and not expandable</returns>
    public GameObject Get(Vector3 position = default, Quaternion rotation = default)
    {
        GameObject obj = null;
        
        // Try to get from pool
        if (pool.Count > 0)
        {
            obj = pool.Dequeue();
        }
        // Create new if expandable and under max size
        else if (expandable && TotalCount < maxSize)
        {
            obj = Object.Instantiate(prefab, poolParent);
        }
        // Pool is at capacity
        else
        {
            Debug.LogWarning($"SimpleObjectPool: Pool for {prefab.name} is at maximum capacity ({maxSize})");
            return null;
        }
        
        if (obj != null)
        {
            obj.transform.position = position;
            obj.transform.rotation = rotation;
            obj.SetActive(true);
            activeObjects.Add(obj);
        }
        
        return obj;
    }
    
    /// <summary>
    /// Return an object to the pool
    /// </summary>
    /// <param name="obj">The object to return</param>
    /// <param name="resetAction">Optional action to reset the object state</param>
    public void Return(GameObject obj, System.Action<GameObject> resetAction = null)
    {
        if (obj == null || !activeObjects.Contains(obj))
            return;
            
        activeObjects.Remove(obj);
        
        // Reset the object if action provided
        resetAction?.Invoke(obj);
        
        obj.SetActive(false);
        pool.Enqueue(obj);
    }
    
    /// <summary>
    /// Check if an object belongs to this pool
    /// </summary>
    public bool Contains(GameObject obj)
    {
        return activeObjects.Contains(obj) || pool.Contains(obj);
    }
    
    /// <summary>
    /// Clear the entire pool
    /// </summary>
    public void Clear()
    {
        // Destroy all active objects
        foreach (var obj in activeObjects)
        {
            if (obj != null)
                Object.Destroy(obj);
        }
        activeObjects.Clear();
        
        // Destroy all pooled objects
        while (pool.Count > 0)
        {
            var obj = pool.Dequeue();
            if (obj != null)
                Object.Destroy(obj);
        }
        
        Debug.Log($"SimpleObjectPool: Cleared pool for {prefab?.name}");
    }
    
    /// <summary>
    /// Get pool statistics as a formatted string
    /// </summary>
    public string GetStats()
    {
        return $"Pool: {pool.Count}, Active: {activeObjects.Count}, Total: {TotalCount}/{maxSize}";
    }
}

/// <summary>
/// MonoBehaviour wrapper for SimpleObjectPool to use in the inspector
/// </summary>
public class SimpleObjectPoolManager : MonoBehaviour
{
    [SerializeField] private SimpleObjectPool pool = new SimpleObjectPool();
    [SerializeField] private GameObject prefab;
    [SerializeField] private int initialSize = 10;
    [SerializeField] private int maxSize = 50;
    [SerializeField] private bool expandable = true;
    
    private void Start()
    {
        if (prefab != null)
        {
            pool.Initialize(prefab, initialSize, maxSize, transform);
        }
        else
        {
            Debug.LogError("SimpleObjectPoolManager: No prefab assigned!");
        }
    }
    
    public GameObject Get(Vector3 position = default, Quaternion rotation = default)
    {
        return pool.Get(position, rotation);
    }
    
    public void Return(GameObject obj, System.Action<GameObject> resetAction = null)
    {
        pool.Return(obj, resetAction);
    }
    
    public bool Contains(GameObject obj)
    {
        return pool.Contains(obj);
    }
    
    public string GetStats()
    {
        return pool.GetStats();
    }
    
    private void OnDestroy()
    {
        pool.Clear();
    }
}
