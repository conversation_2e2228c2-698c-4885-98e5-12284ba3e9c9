<linker>
	<assembly fullname="Assembly-CSharp">
		<type fullname="Armor" preserve="nothing"/>
		<type fullname="AudioSystem.AudioEventChannel" preserve="nothing"/>
		<type fullname="AudioSystem.AudioEventDefinition" preserve="nothing"/>
		<type fullname="AudioSystem.DynamicReverbProbe" preserve="nothing"/>
		<type fullname="AudioSystem.GlobalAudioManager" preserve="nothing"/>
		<type fullname="AudioSystem.PlayerAudioHandler" preserve="nothing"/>
		<type fullname="AutoSafetySystem" preserve="nothing"/>
		<type fullname="Bag" preserve="nothing"/>
		<type fullname="BatteryController" preserve="nothing"/>
		<type fullname="ClimbingRopeSystem" preserve="nothing"/>
		<type fullname="ConsumableDefinition" preserve="nothing"/>
		<type fullname="CrosshairManager" preserve="nothing"/>
		<type fullname="CubeLevelEditor" preserve="nothing"/>
		<type fullname="CurrencyManager" preserve="nothing"/>
		<type fullname="CustomCursor" preserve="nothing"/>
		<type fullname="DeathManager" preserve="nothing"/>
		<type fullname="DebugFlyController" preserve="nothing"/>
		<type fullname="DevSceneInitializer" preserve="nothing"/>
		<type fullname="DynamicFogController" preserve="nothing"/>
		<type fullname="DynamicLightIntensity" preserve="nothing"/>
		<type fullname="EquipmentManager" preserve="nothing"/>
		<type fullname="FallDamageSystem" preserve="nothing"/>
		<type fullname="Flare" preserve="nothing"/>
		<type fullname="FlareGun" preserve="nothing"/>
		<type fullname="FlarePool" preserve="nothing"/>
		<type fullname="FPSControllerSettingsUI" preserve="nothing"/>
		<type fullname="FPSPlayerManager" preserve="nothing"/>
		<type fullname="GrabInteraction" preserve="nothing"/>
		<type fullname="GrapplingHookSystem" preserve="nothing"/>
		<type fullname="HeadBob" preserve="nothing"/>
		<type fullname="HeadBobDebug" preserve="nothing"/>
		<type fullname="HeavyObjectManager" preserve="nothing"/>
		<type fullname="Helmet" preserve="nothing"/>
		<type fullname="HingeRotator" preserve="nothing"/>
		<type fullname="InteractableObject" preserve="nothing"/>
		<type fullname="InteractionManager" preserve="nothing"/>
		<type fullname="InteractiveWorldScreen" preserve="nothing"/>
		<type fullname="InvDragAndDropManager" preserve="nothing"/>
		<type fullname="InvDroppedStorageEquipment" preserve="nothing"/>
		<type fullname="InvItemDropping" preserve="nothing"/>
		<type fullname="InvItemPickup" preserve="nothing"/>
		<type fullname="InvItemSplitter" preserve="nothing"/>
		<type fullname="InvUI" preserve="nothing"/>
		<type fullname="ItemRegistry" preserve="nothing"/>
		<type fullname="ItemUniqueId" preserve="nothing"/>
		<type fullname="JPEGCompression" preserve="nothing"/>
		<type fullname="JPEGCompressionController" preserve="nothing"/>
		<type fullname="KinematicCharacterController.FPS.FPSCharacterCamera" preserve="nothing"/>
		<type fullname="KinematicCharacterController.FPS.FPSCharacterController" preserve="nothing"/>
		<type fullname="KinematicCharacterController.FPS.SpaceshipCameraController" preserve="nothing"/>
		<type fullname="KinematicCharacterController.KinematicCharacterMotor" preserve="nothing"/>
		<type fullname="KinematicClimbingSystem" preserve="nothing"/>
		<type fullname="KinematicPlatform" preserve="nothing"/>
		<type fullname="KinematicPlatformPassengerZone" preserve="nothing"/>
		<type fullname="KinematicPlatformPersistence" preserve="nothing"/>
		<type fullname="KinematicVaultSystem" preserve="nothing"/>
		<type fullname="KinematicWallRun" preserve="nothing"/>
		<type fullname="Manual" preserve="nothing"/>
		<type fullname="MenuCameraId" preserve="nothing"/>
		<type fullname="MirrorOrbitCamera" preserve="nothing"/>
		<type fullname="MoneyCounterAnimation" preserve="nothing"/>
		<type fullname="MoneyCounterInitializer" preserve="nothing"/>
		<type fullname="NotificationManager" preserve="nothing"/>
		<type fullname="ObjectRecoveryManager" preserve="nothing"/>
		<type fullname="PauseMenuManager" preserve="nothing"/>
		<type fullname="PersistenceManager" preserve="nothing"/>
		<type fullname="PlatformButtonController" preserve="nothing"/>
		<type fullname="PlayerBoonManager" preserve="nothing"/>
		<type fullname="PlayerDebugDisplay" preserve="nothing"/>
		<type fullname="PlayerStatus" preserve="nothing"/>
		<type fullname="RagdollTumbleSystem" preserve="nothing"/>
		<type fullname="RocketJumpEffect" preserve="nothing"/>
		<type fullname="SettingsAudioManager" preserve="nothing"/>
		<type fullname="ShopSystem" preserve="nothing"/>
		<type fullname="ShopUI" preserve="nothing"/>
		<type fullname="SpaceshipVehicle" preserve="nothing"/>
		<type fullname="StartMenuManager" preserve="nothing"/>
		<type fullname="StashSystem" preserve="nothing"/>
		<type fullname="StashUI" preserve="nothing"/>
		<type fullname="ToolDefinition" preserve="nothing"/>
		<type fullname="ToolModelManager" preserve="nothing"/>
		<type fullname="ToolSelectionController" preserve="nothing"/>
		<type fullname="ToolSelectionManager" preserve="nothing"/>
		<type fullname="ToolSelectionUILoader" preserve="nothing"/>
		<type fullname="TraderInventory" preserve="nothing"/>
		<type fullname="UIPanelManager" preserve="nothing"/>
		<type fullname="VehicleInteractable" preserve="nothing"/>
		<type fullname="ViewModelHeadBob" preserve="nothing"/>
		<type fullname="VoidRescueSystem" preserve="nothing"/>
		<type fullname="WakeUpPoint" preserve="nothing"/>
		<type fullname="WireCreatorWindow/WireComponent" preserve="nothing"/>
		<type fullname="WorldItemManager" preserve="nothing"/>
	</assembly>
	<assembly fullname="BakeryRuntimeAssembly">
		<type fullname="BakeryLightmapGroup" preserve="nothing"/>
		<type fullname="BakeryLightMesh" preserve="nothing"/>
		<type fullname="BakeryPointLight" preserve="nothing"/>
		<type fullname="BakeryProjectSettings" preserve="nothing"/>
		<type fullname="BakerySector" preserve="nothing"/>
		<type fullname="BakeryVolume" preserve="nothing"/>
		<type fullname="ftGlobalStorage" preserve="nothing"/>
		<type fullname="ftLightmapsStorage" preserve="nothing"/>
		<type fullname="ftLocalStorage" preserve="nothing"/>
	</assembly>
	<assembly fullname="Tayx.Graphy">
		<type fullname="Graphy.Runtime.UI.G_SafeArea" preserve="nothing"/>
		<type fullname="Tayx.Graphy.Advanced.G_AdvancedData" preserve="nothing"/>
		<type fullname="Tayx.Graphy.Audio.G_AudioGraph" preserve="nothing"/>
		<type fullname="Tayx.Graphy.Audio.G_AudioManager" preserve="nothing"/>
		<type fullname="Tayx.Graphy.Audio.G_AudioMonitor" preserve="nothing"/>
		<type fullname="Tayx.Graphy.Audio.G_AudioText" preserve="nothing"/>
		<type fullname="Tayx.Graphy.Fps.G_FpsGraph" preserve="nothing"/>
		<type fullname="Tayx.Graphy.Fps.G_FpsManager" preserve="nothing"/>
		<type fullname="Tayx.Graphy.Fps.G_FpsMonitor" preserve="nothing"/>
		<type fullname="Tayx.Graphy.Fps.G_FpsText" preserve="nothing"/>
		<type fullname="Tayx.Graphy.GraphyDebugger" preserve="nothing"/>
		<type fullname="Tayx.Graphy.GraphyManager" preserve="nothing"/>
		<type fullname="Tayx.Graphy.Ram.G_RamGraph" preserve="nothing"/>
		<type fullname="Tayx.Graphy.Ram.G_RamManager" preserve="nothing"/>
		<type fullname="Tayx.Graphy.Ram.G_RamMonitor" preserve="nothing"/>
		<type fullname="Tayx.Graphy.Ram.G_RamText" preserve="nothing"/>
	</assembly>
	<assembly fullname="Unity.RenderPipelines.Core.Runtime">
		<type fullname="UnityEngine.Rendering.ProbeVolume" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.ProbeVolumesOptions" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerBitField" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerButton" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerCanvas" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerColor" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerContainer" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerEnumField" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerEnumHistory" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerFloatField" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerFoldout" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerGroup" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerHBox" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerIndirectFloatField" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerIndirectToggle" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerIntField" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerMessageBox" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerObject" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerObjectList" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerObjectPopupField" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerPanel" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerPersistentCanvas" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerProgressBar" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerRenderingLayerField" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerRow" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerToggle" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerToggleHistory" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerUIntField" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerValue" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerValueTuple" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerVBox" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerVector2" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerVector3" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.DebugUIHandlerVector4" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.UI.UIFoldout" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.Volume" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.VolumeProfile" preserve="nothing"/>
	</assembly>
	<assembly fullname="Unity.RenderPipelines.HighDefinition.Runtime">
		<type fullname="UnityEngine.Rendering.HighDefinition.Bloom" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.ChannelMixer" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.ChromaticAberration" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.CloudLayer" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.ColorAdjustments" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.ColorCurves" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.ComputeMaterialLibrary" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.ContactShadows" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.CustomPassVolume" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.DepthOfField" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.DiffusionProfileList" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.DiffusionProfileSettings" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.Exposure" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.FilmGrain" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.Fog" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.GlobalIllumination" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.GradientSky" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.HDAdditionalCameraData" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.HDAdditionalLightData" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.HDRenderPipelineAsset" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.HDRenderPipelineGlobalSettings" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.HDRISky" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.HDShadowSettings" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.HighQualityLineRenderingVolumeComponent" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.IndirectLightingController" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.LensDistortion" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.LiftGammaGain" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.LightCluster" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.LocalVolumetricFog" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.MicroShadowing" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.MotionBlur" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.PaniniProjection" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.PathTracing" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.PhysicallyBasedSky" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.RayTracingSettings" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.RecursiveRendering" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.ScreenSpaceAmbientOcclusion" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.ScreenSpaceLensFlare" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.ScreenSpaceReflection" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.ScreenSpaceRefraction" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.ShadowsMidtonesHighlights" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.SplitToning" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.StaticLightingSky" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.SubSurfaceScattering" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.Tonemapping" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.Vignette" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.VisualEnvironment" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.VolumetricClouds" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.WaterRendering" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.WaterSurface" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.HighDefinition.WhiteBalance" preserve="nothing"/>
	</assembly>
	<assembly fullname="Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime">
		<type fullname="UnityEngine.Rendering.HighDefinition.RequiredSettingsSO_HDRP" preserve="nothing"/>
	</assembly>
	<assembly fullname="Unity.TextMeshPro">
		<type fullname="TMPro.TMP_FontAsset" preserve="nothing"/>
		<type fullname="TMPro.TMP_Settings" preserve="nothing"/>
		<type fullname="TMPro.TMP_SpriteAsset" preserve="nothing"/>
		<type fullname="TMPro.TMP_StyleSheet" preserve="nothing"/>
	</assembly>
	<assembly fullname="Unity.VisualEffectGraph.Runtime">
		<type fullname="UnityEngine.VFX.VFXRuntimeResources" preserve="nothing"/>
	</assembly>
	<assembly fullname="UnityEngine">
		<type fullname="UnityEditor.AudioManager" preserve="nothing"/>
		<type fullname="UnityEditor.InputManager" preserve="nothing"/>
		<type fullname="UnityEditor.MonoManager" preserve="nothing"/>
		<type fullname="UnityEditor.MonoScript" preserve="nothing"/>
		<type fullname="UnityEditor.Physics2DSettings" preserve="nothing"/>
		<type fullname="UnityEditor.PlayerSettings" preserve="nothing"/>
		<type fullname="UnityEditor.TagManager" preserve="nothing"/>
		<type fullname="UnityEditor.TimeManager" preserve="nothing"/>
		<type fullname="UnityEditor.VFXManager" preserve="nothing"/>
		<type fullname="UnityEngine.Audio.AudioMixer" preserve="nothing"/>
		<type fullname="UnityEngine.Audio.AudioMixerGroup" preserve="nothing"/>
		<type fullname="UnityEngine.Audio.AudioMixerSnapshot" preserve="nothing"/>
		<type fullname="UnityEngine.Audio.AudioResource" preserve="nothing"/>
		<type fullname="UnityEngine.AudioBehaviour" preserve="nothing"/>
		<type fullname="UnityEngine.AudioClip" preserve="nothing"/>
		<type fullname="UnityEngine.AudioListener" preserve="nothing"/>
		<type fullname="UnityEngine.AudioLowPassFilter" preserve="nothing"/>
		<type fullname="UnityEngine.AudioReverbZone" preserve="nothing"/>
		<type fullname="UnityEngine.AudioSource" preserve="nothing"/>
		<type fullname="UnityEngine.Behaviour" preserve="nothing"/>
		<type fullname="UnityEngine.BoxCollider" preserve="nothing"/>
		<type fullname="UnityEngine.Camera" preserve="nothing"/>
		<type fullname="UnityEngine.Canvas" preserve="nothing"/>
		<type fullname="UnityEngine.CanvasRenderer" preserve="nothing"/>
		<type fullname="UnityEngine.CapsuleCollider" preserve="nothing"/>
		<type fullname="UnityEngine.Collider" preserve="nothing"/>
		<type fullname="UnityEngine.Component" preserve="nothing"/>
		<type fullname="UnityEngine.ComputeShader" preserve="nothing"/>
		<type fullname="UnityEngine.Cubemap" preserve="nothing"/>
		<type fullname="UnityEngine.Font" preserve="nothing"/>
		<type fullname="UnityEngine.GameObject" preserve="nothing"/>
		<type fullname="UnityEngine.Light" preserve="nothing"/>
		<type fullname="UnityEngine.LightingSettings" preserve="nothing"/>
		<type fullname="UnityEngine.LightmapSettings" preserve="nothing"/>
		<type fullname="UnityEngine.LightProbes" preserve="nothing"/>
		<type fullname="UnityEngine.LineRenderer" preserve="nothing"/>
		<type fullname="UnityEngine.Material" preserve="nothing"/>
		<type fullname="UnityEngine.Mesh" preserve="nothing"/>
		<type fullname="UnityEngine.MeshCollider" preserve="nothing"/>
		<type fullname="UnityEngine.MeshFilter" preserve="nothing"/>
		<type fullname="UnityEngine.MeshRenderer" preserve="nothing"/>
		<type fullname="UnityEngine.MonoBehaviour" preserve="nothing"/>
		<type fullname="UnityEngine.Object" preserve="nothing"/>
		<type fullname="UnityEngine.ParticleSystem" preserve="nothing"/>
		<type fullname="UnityEngine.ParticleSystemRenderer" preserve="nothing"/>
		<type fullname="UnityEngine.QualitySettings" preserve="nothing"/>
		<type fullname="UnityEngine.RectTransform" preserve="nothing"/>
		<type fullname="UnityEngine.Renderer" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.GraphicsSettings" preserve="nothing"/>
		<type fullname="UnityEngine.RenderSettings" preserve="nothing"/>
		<type fullname="UnityEngine.Rigidbody" preserve="nothing"/>
		<type fullname="UnityEngine.Shader" preserve="nothing"/>
		<type fullname="UnityEngine.SphereCollider" preserve="nothing"/>
		<type fullname="UnityEngine.Sprite" preserve="nothing"/>
		<type fullname="UnityEngine.Terrain" preserve="nothing"/>
		<type fullname="UnityEngine.TerrainCollider" preserve="nothing"/>
		<type fullname="UnityEngine.TerrainData" preserve="nothing"/>
		<type fullname="UnityEngine.TextAsset" preserve="nothing"/>
		<type fullname="UnityEngine.Texture" preserve="nothing"/>
		<type fullname="UnityEngine.Texture2D" preserve="nothing"/>
		<type fullname="UnityEngine.Texture3D" preserve="nothing"/>
		<type fullname="UnityEngine.Transform" preserve="nothing"/>
	</assembly>
	<assembly fullname="UnityEngine.UI">
		<type fullname="UnityEngine.EventSystems.EventSystem" preserve="nothing"/>
		<type fullname="UnityEngine.EventSystems.EventTrigger" preserve="nothing"/>
		<type fullname="UnityEngine.EventSystems.StandaloneInputModule" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Button" preserve="nothing"/>
		<type fullname="UnityEngine.UI.CanvasScaler" preserve="nothing"/>
		<type fullname="UnityEngine.UI.ContentSizeFitter" preserve="nothing"/>
		<type fullname="UnityEngine.UI.GraphicRaycaster" preserve="nothing"/>
		<type fullname="UnityEngine.UI.HorizontalLayoutGroup" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Image" preserve="nothing"/>
		<type fullname="UnityEngine.UI.LayoutElement" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Mask" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Scrollbar" preserve="nothing"/>
		<type fullname="UnityEngine.UI.ScrollRect" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Shadow" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Text" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Toggle" preserve="nothing"/>
		<type fullname="UnityEngine.UI.VerticalLayoutGroup" preserve="nothing"/>
	</assembly>
	<assembly fullname="UnityEngine.UIElementsModule">
		<type fullname="UnityEngine.UIElements.PanelSettings" preserve="nothing"/>
		<type fullname="UnityEngine.UIElements.StyleSheet" preserve="nothing"/>
		<type fullname="UnityEngine.UIElements.ThemeStyleSheet" preserve="nothing"/>
		<type fullname="UnityEngine.UIElements.UIDocument" preserve="nothing"/>
		<type fullname="UnityEngine.UIElements.VisualTreeAsset" preserve="nothing"/>
	</assembly>
</linker>
