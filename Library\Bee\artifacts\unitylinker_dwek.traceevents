{ "pid": 87404, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "UnityLinker" } },
{ "pid": 87404, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 87404, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 87404, "tid": 1, "ts": 1755814021591526, "dur": 1629098, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 87404, "tid": 1, "ts": 1755814021594381, "dur": 384106, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 87404, "tid": 1, "ts": 1755814021896371, "dur": 81277, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 87404, "tid": 1, "ts": 1755814022036303, "dur": 16992, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 87404, "tid": 1, "ts": 1755814022054685, "dur": 140279, "ph": "X", "name": "CopyModeStep", "args": {} },
{ "pid": 87404, "tid": 1, "ts": 1755814022195005, "dur": 765249, "ph": "X", "name": "LoadReferencesStep", "args": {} },
{ "pid": 87404, "tid": 1, "ts": 1755814022960270, "dur": 245800, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 87404, "tid": 1, "ts": 1755814023217290, "dur": 3170, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 87404, "tid": 1, "ts": 1755814023220625, "dur": 436, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 87404, "tid": 1, "ts": 1755814023232288, "dur": 3255, "ph": "X", "name": "", "args": {} },
{ "pid": 87404, "tid": 1, "ts": 1755814023231008, "dur": 4831, "ph": "X", "name": "Write chrome-trace events", "args": {} },
