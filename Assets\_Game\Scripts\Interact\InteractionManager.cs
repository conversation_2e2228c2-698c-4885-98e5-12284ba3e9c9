using UnityEngine;
using KinematicCharacterController.FPS;

public class InteractionManager : MonoBehaviour
{
    [<PERSON><PERSON>("Interaction Settings")]
    [Tooltip("Maximum distance at which the player can interact with objects.")]
    [SerializeField] private float maxInteractionDistance = 3f;

    [Tooltip("Key to press for interacting.")]
    [SerializeField] private KeyCode interactKey = KeyCode.F;

    [<PERSON><PERSON>("Optimization Settings")]
    [Tooltip("How often to update interaction highlighting (in seconds)")]
    [SerializeField] private float raycastUpdateInterval = 0.1f;
    
    [Tooltip("Minimum camera movement before forcing a raycast update")]
    [SerializeField] private float cameraMovementThreshold = 0.01f;
    
    [Tooltip("Minimum camera rotation before forcing a raycast update (degrees)")]
    [SerializeField] private float cameraRotationThreshold = 1f;

    [Header("References")]
    [Tooltip("Reference to the player's camera component.")]
    [SerializeField] private FPSCharacterCamera cameraController;
    
    [Toolt<PERSON>("Reference to the player's main camera.")]
    [SerializeField] private Camera playerCamera;
    
    [<PERSON><PERSON><PERSON>("Reference to the grab interaction component")]
    [SerializeField] private GrabInteraction grabInteraction;

    // Current highlighted interactable
    private IInteractable currentInteractable;
    
    // Track key press timing
    private float keyPressTime = 0f;
    private bool waitingForKeyRelease = false;
    
    // Raycast optimization
    private float lastRaycastTime = 0f;
    private Vector3 lastCameraPosition;
    private Quaternion lastCameraRotation;
    
    // Cached raycast results
    private RaycastHit cachedHitInfo;
    private bool hasCachedHit = false;
    private float cacheTime = 0f;
    private const float CACHE_DURATION = 0.05f; // Cache results for 50ms

    // Hold-to-interact state (e.g., hinge drag)
    private bool holdActive = false;
    private IHoldInteractable currentHoldTarget;
    
    // Pre-allocated arrays for NonAlloc physics calls
    private Collider[] overlapResults = new Collider[16];
    private RaycastHit[] raycastResults = new RaycastHit[8];

    private void Awake()
    {
        // Make sure the GrabInteraction component exists
        if (grabInteraction == null)
        {
            grabInteraction = GetComponent<GrabInteraction>();
            if (grabInteraction == null)
            {
                grabInteraction = gameObject.AddComponent<GrabInteraction>();
                Debug.Log("Added GrabInteraction component");
            }
        }
    }

    private void Start()
    {
        // If camera controller not set, try to find it
        if (cameraController == null)
        {
            cameraController = FindObjectOfType<FPSCharacterCamera>();
            if (cameraController != null)
            {
                playerCamera = cameraController.Camera;
            }
        }
        
        // If we still don't have the player camera, try to find it directly
        if (playerCamera == null)
        {
            playerCamera = Camera.main;
            Debug.LogWarning("Player camera not assigned. Using Camera.main as fallback.");
        }
        
        // Initialize camera tracking
        if (playerCamera != null)
        {
            lastCameraPosition = playerCamera.transform.position;
            lastCameraRotation = playerCamera.transform.rotation;
        }
        
        // Initialize the grab interaction with the camera
        if (grabInteraction != null && playerCamera != null)
        {
            grabInteraction.Initialize(playerCamera);
            Debug.Log($"Initialized GrabInteraction with camera: {playerCamera.name}");
        }
        else
        {
            Debug.LogError("Failed to initialize GrabInteraction: " + 
                          (grabInteraction == null ? "component is null" : "camera is null"));
        }
    }

    private void Update()
    {
        if (playerCamera == null) return;
        
        // If a hold-driven interactable is active, route input to it and skip normal flow
        if (holdActive)
        {
            if (currentHoldTarget == null)
            {
                EndHold(false);
                return;
            }

            // Feed mouse deltas while key is held
            if (Input.GetKey(interactKey))
            {
                float dx = Input.GetAxis("Mouse X");
                float dy = Input.GetAxis("Mouse Y");
                currentHoldTarget.OnHold(gameObject, dx, dy);
            }

            // End hold on key up
            if (Input.GetKeyUp(interactKey))
            {
                EndHold(false);
            }
            return;
        }

        // If grab system is active, clear highlight and let it handle everything
        if (grabInteraction.IsGrabbing) 
        {
            ClearCurrentHighlight();
            return;
        }

        // Handle key press for interaction vs. grab
        if (Input.GetKeyDown(interactKey))
        {
            keyPressTime = Time.time;
            waitingForKeyRelease = true;
        }
        
        // If key is held long enough, start hold if supported, otherwise try to grab
        if (waitingForKeyRelease && Input.GetKey(interactKey))
        {
            if (Time.time - keyPressTime > grabInteraction.GrabHoldTime && !grabInteraction.IsGrabbing)
            {
                // Prefer hold-enabled interactables over grab when available
                IHoldInteractable holdCandidate = currentInteractable as IHoldInteractable;
                if (holdCandidate != null && holdCandidate.WantsHold(gameObject))
                {
                    StartHold(holdCandidate);
                    waitingForKeyRelease = false;
                }
                else
                {
                    // Try to grab instead of interact
                    grabInteraction.AttemptGrab();
                    waitingForKeyRelease = false; // Handled, no longer waiting
                }
            }
        }
        
        // If key is released before grab threshold, handle as normal interaction
        if (waitingForKeyRelease && Input.GetKeyUp(interactKey))
        {
            waitingForKeyRelease = false;
            // Only interact if we didn't start grabbing
            if (!grabInteraction.IsGrabbing)
            {
                AttemptInteraction();
            }
        }
        
        // Update highlighting with optimized raycasting
        if (ShouldUpdateRaycast())
        {
            UpdateInteractionHighlight();
        }
    }

    private void StartHold(IHoldInteractable target)
    {
        ClearCurrentHighlight(); // Clear highlight when entering hold state
        currentHoldTarget = target;
        holdActive = true;
        currentHoldTarget.OnHoldStarted(gameObject);
        if (cameraController != null)
        {
            cameraController.SetGrabRotationMode(true);
        }
    }

    private void EndHold(bool canceled)
    {
        if (!holdActive) return;
        try
        {
            currentHoldTarget?.OnHoldEnded(gameObject, canceled);
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"Error ending hold interaction: {ex.Message}");
        }
        finally
        {
            holdActive = false;
            currentHoldTarget = null;
            if (cameraController != null)
            {
                cameraController.SetGrabRotationMode(false);
            }
            // Clear highlight on exit failure paths
            ClearCurrentHighlight();
        }
    }

    /// <summary>
    /// Determines if we should perform a new raycast based on time and camera movement
    /// </summary>
    private bool ShouldUpdateRaycast()
    {
        // Check if enough time has passed since last raycast
        float timeSinceLastRaycast = Time.time - lastRaycastTime;
        bool timeThresholdMet = timeSinceLastRaycast >= raycastUpdateInterval;
        
        // Check if camera has moved significantly
        float positionDelta = Vector3.Distance(playerCamera.transform.position, lastCameraPosition);
        bool positionThresholdMet = positionDelta > cameraMovementThreshold;
        
        // Check if camera has rotated significantly
        float rotationDelta = Quaternion.Angle(playerCamera.transform.rotation, lastCameraRotation);
        bool rotationThresholdMet = rotationDelta > cameraRotationThreshold;
        
        // Update if any threshold is met
        return timeThresholdMet || positionThresholdMet || rotationThresholdMet;
    }

    /// <summary>
    /// Updates which object is currently highlighted for interaction
    /// </summary>
    private void UpdateInteractionHighlight()
    {
        // Update tracking variables
        lastRaycastTime = Time.time;
        lastCameraPosition = playerCamera.transform.position;
        lastCameraRotation = playerCamera.transform.rotation;
        
        // Perform the raycast
        PerformInteractionRaycast();
        
        // Process the raycast result
        IInteractable newInteractable = null;
        if (hasCachedHit)
        {
            IInteractable interactable = cachedHitInfo.collider.GetComponent<IInteractable>();
            if (interactable != null && interactable.CanInteract(gameObject))
            {
                newInteractable = interactable;
            }
        }
        
        // Update highlighting if the interactable has changed
        if (newInteractable != currentInteractable)
        {
            // Remove highlight from previous interactable
            if (currentInteractable != null)
            {
                currentInteractable.RemoveHighlight();
            }
            
            // Add highlight to new interactable
            currentInteractable = newInteractable;
            if (currentInteractable != null)
            {
                currentInteractable.Highlight();
            }
        }
    }

    /// <summary>
    /// Performs a raycast and caches the result with improved targeting
    /// </summary>
    private void PerformInteractionRaycast()
    {
        // Check if we have a valid cached result
        if (Time.time - cacheTime < CACHE_DURATION && hasCachedHit)
        {
            return; // Use cached result
        }
        
        // First try direct raycast for precise targeting
        Ray ray = new Ray(playerCamera.transform.position, playerCamera.transform.forward);
        hasCachedHit = Physics.Raycast(ray, out cachedHitInfo, maxInteractionDistance);
        
        // If direct raycast hit an interactable, use it
        if (hasCachedHit)
        {
            IInteractable directHit = cachedHitInfo.collider.GetComponent<IInteractable>();
            if (directHit != null && directHit.CanInteract(gameObject))
            {
                cacheTime = Time.time;
                return; // Use the direct hit
            }
        }
        
        // If no direct interactable hit, try sphere overlap for more forgiving selection
         IInteractable bestCandidate = FindBestInteractableCandidate();
         if (bestCandidate != null)
         {
             // Perform a direct raycast to the best candidate to get proper hit info
             GameObject candidateObj = (bestCandidate as MonoBehaviour)?.gameObject;
             if (candidateObj != null)
             {
                 Vector3 directionToCandidate = (candidateObj.transform.position - playerCamera.transform.position).normalized;
                 Ray candidateRay = new Ray(playerCamera.transform.position, directionToCandidate);
                 
                 // Try to raycast directly to the candidate object
                 if (Physics.Raycast(candidateRay, out RaycastHit candidateHit, maxInteractionDistance))
                 {
                     // Check if we hit the intended candidate or something in front of it
                     if (candidateHit.collider.gameObject == candidateObj || 
                         candidateHit.collider.GetComponent<IInteractable>() == bestCandidate)
                     {
                         cachedHitInfo = candidateHit;
                         hasCachedHit = true;
                     }
                 }
             }
         }
        
        cacheTime = Time.time;
    }

    /// <summary>
    /// Attempts to interact with an object the player is looking at.
    /// </summary>
    private void AttemptInteraction()
    {
        // Use cached raycast result if it's recent enough
        if (Time.time - cacheTime < CACHE_DURATION && hasCachedHit)
        {
            IInteractable interactable = cachedHitInfo.collider.GetComponent<IInteractable>();
            if (interactable != null && interactable.CanInteract(gameObject))
            {
                interactable.Interact(gameObject);
            }
            return;
        }
        
        // Otherwise perform the improved interaction detection
        PerformInteractionRaycast();
        
        if (hasCachedHit)
        {
            IInteractable interactable = cachedHitInfo.collider.GetComponent<IInteractable>();
            if (interactable != null && interactable.CanInteract(gameObject))
            {
                interactable.Interact(gameObject);
            }
        }
    }
    
    /// <summary>
    /// Finds the best interactable candidate using sphere overlap when direct raycast fails
    /// </summary>
    /// <returns>The best interactable candidate, or null if none found</returns>
    private IInteractable FindBestInteractableCandidate()
    {
        // Use a smaller sphere for better performance
        float sphereRadius = 1.0f; // Reduced from 1.5f for performance
        Vector3 sphereCenter = playerCamera.transform.position + playerCamera.transform.forward * (maxInteractionDistance * 0.5f);
        
        // Use NonAlloc version to avoid memory allocations
        int hitCount = Physics.OverlapSphereNonAlloc(sphereCenter, sphereRadius, overlapResults);
        
        IInteractable bestCandidate = null;
        float bestScore = float.MaxValue;
        int candidatesProcessed = 0;
        const int maxCandidates = 8; // Limit processing for performance
        const float earlyExitThreshold = 0.1f; // Exit early if we find a very good candidate
        
        for (int i = 0; i < hitCount; i++)
        {
            // Performance limit: don't process too many candidates
            if (candidatesProcessed >= maxCandidates)
                break;
                
            Collider col = overlapResults[i];
            IInteractable interactable = col.GetComponent<IInteractable>();
            if (interactable == null || !interactable.CanInteract(gameObject))
                continue;
                
            candidatesProcessed++;
                
            // Calculate distance from camera
            Vector3 colPosition = col.transform.position;
            Vector3 cameraPosition = playerCamera.transform.position;
            float distance = Vector3.Distance(cameraPosition, colPosition);
            if (distance > maxInteractionDistance)
                continue;
                
            // Calculate how close the object is to the center of the screen
            Vector3 screenPoint = playerCamera.WorldToViewportPoint(colPosition);
            // Avoid Vector2 allocation by calculating distance directly
            float screenCenterX = screenPoint.x - 0.5f;
            float screenCenterY = screenPoint.y - 0.5f;
            float screenDistance = Mathf.Sqrt(screenCenterX * screenCenterX + screenCenterY * screenCenterY);
            
            // Skip objects that are behind the camera or too far off-screen
            if (screenPoint.z < 0 || screenDistance > 0.35f) // Reduced from 0.4f
                continue;
                
            // Simplified size factor calculation for performance
            float sizeFactor = 1.0f;
            Renderer renderer = col.GetComponent<Renderer>();
            if (renderer != null)
            {
                float objectSize = renderer.bounds.size.magnitude;
                sizeFactor = Mathf.Clamp(1.0f / objectSize, 0.1f, 2.0f);
            }
            
            // Calculate final score (lower is better)
            float score = (screenDistance * 2.0f) + (distance * 0.1f) + sizeFactor;
            
            if (score < bestScore)
            {
                bestScore = score;
                bestCandidate = interactable;
                
                // Early exit if we found a very good candidate
                if (bestScore < earlyExitThreshold)
                    break;
            }
        }
        
        return bestCandidate;
    }
    
    /// <summary>
    /// Clears the current highlight without setting a new one
    /// </summary>
    private void ClearCurrentHighlight()
    {
        if (currentInteractable != null)
        {
            currentInteractable.RemoveHighlight();
            currentInteractable = null;
        }
    }
    
    /// <summary>
    /// Clean up highlights when component is disabled
    /// </summary>
    private void OnDisable()
    {
        ClearCurrentHighlight();
        EndHold(true); // Cancel any active hold
    }
    
    /// <summary>
    /// Clean up highlights when component is destroyed
    /// </summary>
    private void OnDestroy()
    {
        ClearCurrentHighlight();
        EndHold(true); // Cancel any active hold
    }
    
    /// <summary>
    /// Returns the current interactable being highlighted
    /// </summary>
    /// <returns>The current interactable, or null if none</returns>
    public IInteractable GetCurrentInteractable()
    {
        return currentInteractable;
    }
}