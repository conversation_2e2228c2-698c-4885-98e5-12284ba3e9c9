using UnityEngine;
using KinematicCharacterController;
using KinematicCharacterController.FPS;
using System.Collections;

public class KinematicVaultSystem : MonoBehaviour
{
    [Header("Detection Settings")]
    [Tooltip("Forward distance to check for ledges")]
    public float forwardCheckDistance = 0.8f;
    [Tooltip("Maximum height above character to detect ledges")]
    public float maxLedgeGrabHeight = 2.5f;
    [Tooltip("Minimum height required for ledge grab")]
    public float minLedgeGrabHeight = 0.5f;
    [Tooltip("Width of the box cast for edge detection")]
    public float edgeDetectionWidth = 0.3f;
    [Tooltip("How far to offset from the wall while hanging")]
    public float hangOffset = 0.3f;
    [Tooltip("Maximum allowed angle (in degrees) of the top surface relative to up")]
    public float topSurfaceMaxAngle = 30f;
    [Tooltip("Maximum allowed horizontal offset from the wall hit to the top edge")]
    public float maxHorizontalOffset = 0.5f;

    [Header("Movement Settings")]
    [Tooltip("How quickly to Lerp to the hang position")]
    public float hangLerpSpeed = 10f;
    [Tooltip("Speed of horizontal movement while hanging")]
    public float hangMoveSpeed = 3f;
    [Tooltip("Speed of the vault motion")]
    public float vaultSpeed = 8f;
    [Tooltip("How high above the ledge to place the character when vaulting")]
    public float vaultHeightOffset = 0.2f;
    [Tooltip("How far forward from the ledge to place the character")]
    public float vaultForwardOffset = 0.5f;
    [Tooltip("Minimum input magnitude needed to automatically vault")]
    public float forwardInputThreshold = 0.1f;

    [Header("Automatic Grab Settings")]
    [Tooltip("Maximum upward velocity that still allows ledge grab")]
    public float maxUpwardGrabVelocity = 4f;
    [Tooltip("Time window to buffer successful edge detections")]
    public float edgeDetectionBuffer = 0.1f;
    [Tooltip("Additional upward velocity tolerance when falling")]
    public float fallingUpwardVelocityTolerance = 3f;
    [Tooltip("Extended detection range when falling")]
    public float fallingDetectionMultiplier = 4f;

    [Header("Layer Filters")]
    [Tooltip("Layers that the character should NOT vault onto (blocks both wall and top surface detection)")]
    public LayerMask vaultBlockedLayers;
    [Tooltip("Debug: Show which layers are being checked")]
    public bool debugLayerChecks = false;

    [Header("References")]
    public KinematicCharacterMotor motor;
    public FPSCharacterController characterController;
    public Transform cameraTransform;
    public FallDamageSystem fallDamageSystem;

    // State tracking
    private bool isHanging = false;
    private bool isVaulting = false;
    private Vector3 grabPoint;
    private Vector3 grabNormal;
    private Vector3 vaultStartPos;
    private Vector3 vaultEndPos;
    private float vaultProgress = 0f;
    private CharacterState previousState;
    private float lastValidEdgeTime;
    private Vector3 lastValidEdgePoint;
    private Vector3 lastValidEdgeNormal;

    // Debug visualization
    private Vector3[] debugPoints = new Vector3[10];
    private int debugPointCount = 0;

    // Suppression control so other systems (e.g., moving platforms) can disable vaulting
    [SerializeField] private bool _vaultSuppressed = false;
    public void SetVaultSuppressed(bool suppressed)
    {
        if (_vaultSuppressed == suppressed) return;
        _vaultSuppressed = suppressed;
        if (_vaultSuppressed)
        {
            // Cancel any ongoing hang or vault to avoid conflicts
            if (isVaulting)
            {
                CompleteVault();
            }
            else if (isHanging)
            {
                StopHang();
            }
        }
    }

    void Start()
    {
        InitializeComponents();
        lastValidEdgeTime = -Mathf.Infinity;
        lastValidEdgePoint = Vector3.zero;
        lastValidEdgeNormal = Vector3.zero;
    }

    void InitializeComponents()
    {
        if (motor == null)
            motor = GetComponent<KinematicCharacterMotor>();
        if (characterController == null)
            characterController = GetComponent<FPSCharacterController>();
        if (cameraTransform == null && Camera.main != null)
            cameraTransform = Camera.main.transform;
        if (fallDamageSystem == null)
            fallDamageSystem = FindObjectOfType<FallDamageSystem>();

        if (!motor || !characterController || !cameraTransform)
        {
            Debug.LogError("VaultSystem: Missing required components!");
            enabled = false;
            return;
        }
        
        if (fallDamageSystem == null)
        {
            Debug.LogWarning("VaultSystem: FallDamageSystem not found. Falling detection will be disabled.");
        }
    }

    void Update()
    {
        if (_vaultSuppressed) return;
        // Only run if in Default or Vaulting states
        if ((characterController.CurrentCharacterState != CharacterState.Default &&
                     characterController.CurrentCharacterState != CharacterState.Vaulting) ||
                    DebugFlyController.IsFlying)
        {return;}
        
        if (isVaulting)
        {
            UpdateVault();
        }
        else if (isHanging)
        {
            UpdateHang();
            
            // Check if player is moving forward to automatically vault
            if (IsMovingForward())
            {
                StartVault();
            }
        }
        else
        {
            CheckForLedgeGrab();
        }
    }

    bool IsMovingForward()
    {
        // Get the forward input from the vertical axis (W/S or Up/Down)
        float forwardInput = Input.GetAxisRaw("Vertical");
        
        // Vault if pressing forward (W or Up) above the threshold
        return forwardInput > forwardInputThreshold;
    }

    #region Ledge Detection
    void CheckForLedgeGrab()
    {
        // Only check for ledges if not grounded
        if (motor.GroundingStatus.IsStableOnGround)
            return;

        debugPointCount = 0;
        Vector3 castStart = GetCastStart();
        Vector3 castDir = GetCastDirection();
        
        // Check if player is falling using FallDamageSystem
        bool isFalling = fallDamageSystem != null && fallDamageSystem.IsFalling();
        
        // More forgiving upward velocity check when falling
        float effectiveMaxUpwardVelocity = maxUpwardGrabVelocity;
        if (isFalling)
        {
            effectiveMaxUpwardVelocity += fallingUpwardVelocityTolerance;
        }
        
        // If moving up too fast, ignore (but be more forgiving when falling)
        if (motor.BaseVelocity.y > effectiveMaxUpwardVelocity)
            return;

        // Calculate allowed vault layers (exclude blocked layers)
        int allowedVaultLayers = motor.CollidableLayers & ~vaultBlockedLayers;
        
        // Use extended detection range when falling
        float detectionDistance = isFalling ? forwardCheckDistance * fallingDetectionMultiplier : forwardCheckDistance;
        float currentEdgeBuffer = edgeDetectionBuffer;
        
        if (debugLayerChecks)
        {
            Debug.Log($"Vault Check - Motor Collidable: {LayerMaskToString(motor.CollidableLayers)}, " +
                     $"Blocked: {LayerMaskToString(vaultBlockedLayers)}, " +
                     $"Allowed: {LayerMaskToString(allowedVaultLayers)}");
            
            if (isFalling)
            {
                Debug.Log($"Falling Detection Active - Detection Distance: {detectionDistance:F2}m, " +
                         $"Edge Buffer: {currentEdgeBuffer:F2}s, " +
                         $"Upward Tolerance: {effectiveMaxUpwardVelocity:F1}m/s");
            }
        }
        
        if (!DetectWallWithBoxCast(castStart, castDir, allowedVaultLayers, detectionDistance, out RaycastHit wallHit))
        {
            // If we recently saw a valid edge, try that
            if (Time.time - lastValidEdgeTime < currentEdgeBuffer)
            {
                TryGrabLastValidEdge();
            }
            return;
        }
        
        // Additional check: Verify the hit object isn't on a blocked layer
        if (IsLayerBlocked(wallHit.collider.gameObject.layer))
        {
            if (debugLayerChecks)
            {
                Debug.Log($"Wall hit blocked - Object: {wallHit.collider.name}, Layer: {LayerMask.LayerToName(wallHit.collider.gameObject.layer)}");
            }
            return;
        }
        
        AddDebugPoint(wallHit.point, Color.yellow);

        // Attempt to find a horizontal ledge above the wall
        if (!TryFindTopEdgeFromWall(wallHit, allowedVaultLayers, out Vector3 topEdgePoint, out Vector3 topEdgeNormal, out GameObject topSurface))
        {
            return;
        }

        // Double-check that the top surface isn't on a blocked layer
        if (topSurface != null && IsLayerBlocked(topSurface.layer))
        {
            if (debugLayerChecks)
            {
                Debug.Log($"Top surface blocked - Object: {topSurface.name}, Layer: {LayerMask.LayerToName(topSurface.layer)}");
            }
            return;
        }

        AddDebugPoint(topEdgePoint, Color.green);

        // Cache the last valid edge
        lastValidEdgeTime = Time.time;
        lastValidEdgePoint = topEdgePoint;
        lastValidEdgeNormal = topEdgeNormal;

        // Start a hang
        StartHang(topEdgePoint, wallHit.normal);
        
        // If already moving forward when ledge is detected, vault immediately
        if (IsMovingForward())
        {
            // Small delay to ensure proper positioning
            StartCoroutine(VaultAfterFrame());
        }
    }
    
    bool IsLayerBlocked(int layer)
    {
        return (vaultBlockedLayers.value & (1 << layer)) != 0;
    }
    
    string LayerMaskToString(LayerMask mask)
    {
        System.Text.StringBuilder sb = new System.Text.StringBuilder();
        for (int i = 0; i < 32; i++)
        {
            if ((mask.value & (1 << i)) != 0)
            {
                if (sb.Length > 0) sb.Append(", ");
                sb.Append(LayerMask.LayerToName(i));
            }
        }
        return sb.ToString();
    }
    
    IEnumerator VaultAfterFrame()
    {
        // Wait a single frame to ensure the hang position is established
        yield return null;
        
        // If still hanging and moving forward, start vault
        if (isHanging && IsMovingForward())
        {
            StartVault();
        }
    }

    Vector3 GetCastStart()
    {
        return motor.TransientPosition + Vector3.up * (motor.Capsule.height * 0.75f);
    }

    Vector3 GetCastDirection()
    {
        Vector3 direction = cameraTransform.forward;
        direction.y = 0f;
        return direction.normalized;
    }
    
    bool DetectWallWithBoxCast(Vector3 origin, Vector3 direction, LayerMask layerMask, float distance, out RaycastHit wallHit)
    {
        Vector3 boxHalfExtents = new Vector3(
            edgeDetectionWidth * 0.5f, 
            0.1f, 
            0.1f
        );
        Quaternion boxRot = Quaternion.LookRotation(direction);

        bool hitSomething = Physics.BoxCast(
            origin,
            boxHalfExtents,
            direction,
            out wallHit,
            boxRot,
            distance,
            layerMask);
            
        if (hitSomething && debugLayerChecks)
        {
            Debug.Log($"BoxCast hit: {wallHit.collider.name} on layer {LayerMask.LayerToName(wallHit.collider.gameObject.layer)} (Distance: {distance:F2}m)");
        }
        
        return hitSomething;
    }

    bool TryFindTopEdgeFromWall(RaycastHit wallHit, LayerMask allowedLayers, out Vector3 finalEdgePoint, out Vector3 finalEdgeNormal, out GameObject topSurfaceObject)
    {
        finalEdgePoint = Vector3.zero;
        finalEdgeNormal = Vector3.zero;
        topSurfaceObject = null;

        // "Up" direction based on the wall's normal
        Vector3 wallUp = Vector3.ProjectOnPlane(Vector3.up, wallHit.normal).normalized;
        if (wallUp.magnitude < 0.01f)
        {
            wallUp = Vector3.Cross(wallHit.normal, Vector3.right).normalized;
        }

        float checkDist = 1.5f;
        Vector3 checkStart = wallHit.point + wallUp * checkDist;

        // Raycast down at a few angles
        for (float angleOffset = -15f; angleOffset <= 15f; angleOffset += 15f)
        {
            Vector3 checkDir = Quaternion.AngleAxis(angleOffset, wallHit.normal) * -wallUp;
            Debug.DrawRay(checkStart, checkDir * (checkDist * 2f), Color.red, 0.5f);

            // Use the passed allowed layers
            if (Physics.Raycast(checkStart, checkDir, out RaycastHit topHit, checkDist * 2f, allowedLayers))
            {
                // Additional layer check for the hit object
                if (IsLayerBlocked(topHit.collider.gameObject.layer))
                {
                    if (debugLayerChecks)
                    {
                        Debug.Log($"Top surface raycast hit blocked layer: {topHit.collider.name} on {LayerMask.LayerToName(topHit.collider.gameObject.layer)}");
                    }
                    continue;
                }
                
                float surfaceAngle = Vector3.Angle(topHit.normal, Vector3.up);
                if (surfaceAngle <= topSurfaceMaxAngle)
                {
                    Vector3 horizontalOffset = Vector3.ProjectOnPlane(topHit.point - wallHit.point, Vector3.up);
                    if (horizontalOffset.magnitude <= maxHorizontalOffset)
                    {
                        // Only accept the candidate if the vertical gap is high enough
                        if (topHit.point.y - wallHit.point.y < minLedgeGrabHeight)
                            continue; // Skip this candidate; it's too low

                        finalEdgePoint = topHit.point;
                        finalEdgeNormal = topHit.normal;
                        topSurfaceObject = topHit.collider.gameObject;
                        
                        if (debugLayerChecks)
                        {
                            Debug.Log($"Valid top surface found: {topHit.collider.name} on layer {LayerMask.LayerToName(topHit.collider.gameObject.layer)}");
                        }
                        
                        return true;
                    }
                }
            }
        }

        return false;
    }

    void TryGrabLastValidEdge()
    {
        Vector3 toEdge = lastValidEdgePoint - motor.TransientPosition;
        float verticalDistance = Mathf.Abs(toEdge.y);
        Vector3 horizontalOffset = Vector3.ProjectOnPlane(toEdge, Vector3.up);
        
        float allowedHorizontalDistance = forwardCheckDistance * 1.5f;

        if (verticalDistance <= maxLedgeGrabHeight &&
            horizontalOffset.magnitude <= allowedHorizontalDistance)
        {
            StartHang(lastValidEdgePoint, lastValidEdgeNormal);
            
            // If already moving forward when grabbing the edge, vault immediately
            if (IsMovingForward())
            {
                StartCoroutine(VaultAfterFrame());
            }
        }
    }
    #endregion

    #region Hanging
    void StartHang(Vector3 point, Vector3 normal)
    {
        Vector3 previousVelocity = motor.BaseVelocity;
        Vector3 previousPosition = motor.TransientPosition;

        grabPoint = point;
        grabNormal = normal;
        isHanging = true;
        isVaulting = false;

        previousState = characterController.CurrentCharacterState;
        characterController.TransitionToState(CharacterState.Vaulting);

        // Instead of snapping, just aim velocity toward the hang
        Vector3 targetPos = GetHangPosition();
        Vector3 toTarget = targetPos - motor.TransientPosition;
        motor.BaseVelocity = toTarget * hangLerpSpeed;

        // Keep some horizontal momentum
        Vector3 horizontalVelocity = Vector3.ProjectOnPlane(previousVelocity, Vector3.up);
        motor.BaseVelocity += horizontalVelocity * 0.3f;

        bool isMovingForward = IsMovingForward();
        
        if (debugLayerChecks)
        {
            Debug.Log("Ledge Grab Started\n" +
                      "Grab Point: " + point.ToString("F2") + "\n" +
                      "Wall Normal: " + normal.ToString("F2") + "\n" +
                      "Previous Velocity: " + previousVelocity.ToString("F2") + "\n" +
                      "Distance to Ledge: " + Vector3.Distance(previousPosition, point).ToString("F2") + "m\n" +
                      "Moving Forward: " + isMovingForward + "\n");
        }
    }

    void UpdateHang()
    {
        Vector3 currentPos = motor.TransientPosition;
        Vector3 targetPos = GetHangPosition();

        // Smooth velocity to approach hang position
        Vector3 newPos = Vector3.Lerp(currentPos, targetPos, Time.deltaTime * hangLerpSpeed);
        Vector3 positionDelta = newPos - currentPos;
        motor.BaseVelocity = positionDelta / Time.deltaTime;

        // Small horizontal movement (shimmy)
        float horizontalInput = Input.GetAxisRaw("Horizontal");
        if (Mathf.Abs(horizontalInput) > 0.1f)
        {
            Vector3 rightDir = Vector3.Cross(Vector3.up, grabNormal).normalized;
            Vector3 lateralMove = rightDir * (horizontalInput * hangMoveSpeed * Time.deltaTime);
            Vector3 newHangPos = currentPos + lateralMove;
            newHangPos = Vector3.Lerp(currentPos, newHangPos, Time.deltaTime * hangLerpSpeed);
            motor.BaseVelocity += (newHangPos - currentPos) / Time.deltaTime;
        }

        // Manual vault on Space and drop on S still work
        if (Input.GetKeyDown(KeyCode.Space))
        {
            StartVault();
        }
        else if (Input.GetKeyDown(KeyCode.S))
        {
            StopHang();
        }
    }

    Vector3 GetHangPosition()
    {
        return grabPoint
               - (grabNormal * hangOffset)
               - (Vector3.up * (motor.Capsule.height * 0.5f));
    }

    void StopHang()
    {
        isHanging = false;
        motor.BaseVelocity = Vector3.down * 2f;
        motor.ForceUnground(0f);
        characterController.TransitionToState(previousState);
    }
    #endregion

    #region Vaulting
    void StartVault()
    {
        isHanging = false;
        isVaulting = true;
        vaultProgress = 0f;

        Vector3 currentVel = motor.BaseVelocity;
        vaultStartPos = motor.TransientPosition;

        // We create a "final" spot in front of the ledge
        Vector3 endGroundPoint = grabPoint + Vector3.up * vaultHeightOffset;
        Vector3 vaultDirection = -grabNormal.normalized;
        if (Vector3.Dot(vaultDirection, Vector3.up) > 0.5f)
        {
            Debug.LogWarning("Vault direction too vertical, using camera forward instead");
            vaultDirection = Vector3.ProjectOnPlane(cameraTransform.forward, Vector3.up).normalized;
        }

        vaultEndPos = endGroundPoint + (vaultDirection * vaultForwardOffset);

        // If there's ground below that final spot, adjust the Y
        // Find landing only on allowed vault layers (exclude blocked)
        int allowedVaultLayers = motor.CollidableLayers & ~vaultBlockedLayers;
        if (Physics.Raycast(vaultEndPos + Vector3.up, Vector3.down, out RaycastHit hit, 2f, allowedVaultLayers))
        {
            // Additional check: ensure landing surface isn't blocked
            if (!IsLayerBlocked(hit.collider.gameObject.layer))
            {
                vaultEndPos.y = hit.point.y + 0.1f;
            }
        }

        // Initial velocity
        Vector3 toEnd = vaultEndPos - vaultStartPos;
        Vector3 initialVaultVelocity = toEnd.normalized * vaultSpeed;

        // Blend with current velocity
        motor.BaseVelocity = Vector3.Lerp(currentVel, initialVaultVelocity, 0.5f);

        if (debugLayerChecks)
        {
            Debug.Log("Vault Started\n" +
                      "Start: " + vaultStartPos.ToString("F2") + "\n" +
                      "End: " + vaultEndPos.ToString("F2") + "\n" +
                      "Distance: " + Vector3.Distance(vaultStartPos, vaultEndPos).ToString("F2") + "m\n");
        }
    }

    void UpdateVault()
    {
        // Advance param from 0 -> 1
        vaultProgress += Time.deltaTime * vaultSpeed;
        vaultProgress = Mathf.Clamp01(vaultProgress);

        // Ensure we stay ungrounded
        motor.ForceUnground(0.1f);

        Vector3 currentPos = motor.TransientPosition;
        Vector3 startPos = vaultStartPos;
        Vector3 endPos = vaultEndPos;

        // 1) Where do we ideally want to be now (including arc)?
        Vector3 horizontalLerp = Vector3.Lerp(startPos, endPos, vaultProgress);
        float arcHeight = GetDynamicVaultArc(vaultProgress);
        Vector3 targetPos = horizontalLerp;
        targetPos.y += arcHeight;

        // 2) Figure out raw velocity
        Vector3 desiredMove = targetPos - currentPos;
        Vector3 rawVelocity = desiredMove / Time.deltaTime;

        // 3) Smooth it to reduce jitter: 
        //    Lerp from current motor velocity toward the raw velocity
        float smoothingFactor = 0.25f; // Adjust for more/less smoothing
        Vector3 smoothedVelocity = Vector3.Lerp(motor.BaseVelocity, rawVelocity, smoothingFactor);

        // 4) Optionally clamp the speed
        float maxVaultSpeed = 15f;
        if (smoothedVelocity.magnitude > maxVaultSpeed)
        {
            smoothedVelocity = smoothedVelocity.normalized * maxVaultSpeed;
        }

        // Assign
        motor.BaseVelocity = smoothedVelocity;

        // 5) Check if we basically arrived
        float distToEnd = Vector3.Distance(currentPos, endPos);
        if (vaultProgress >= 1f || distToEnd < 0.05f)
        {
            CompleteVault();
        }
    }

    void CompleteVault()
    {
        isVaulting = false;
        isHanging = false;

        // Final safe position sweep
        Vector3 safePos = SweepPosition(motor.TransientPosition, vaultEndPos);
        motor.SetTransientPosition(safePos);

        // Give a small forward boost
        Vector3 forwardDir = Vector3.ProjectOnPlane(-grabNormal, Vector3.up).normalized;
        motor.BaseVelocity = forwardDir * (vaultSpeed * 1.5f);

        characterController.TransitionToState(previousState);
    }

    float GetDynamicVaultArc(float t)
    {
        // Basic sine arc
        float sine = Mathf.Sin(t * Mathf.PI);
        float vaultDistance = Vector3.Distance(vaultStartPos, vaultEndPos);

        // Scale the arc up to 25% of capsule height, but also scale with distance
        float baseHeight = motor.Capsule.height * 0.25f;
        float distanceScale = Mathf.Min(vaultDistance * 0.15f, baseHeight);

        return sine * distanceScale;
    }
    #endregion

    #region Sweeps & Overlaps
    Vector3 SweepPosition(Vector3 startPos, Vector3 endPos)
    {
        float distance = Vector3.Distance(startPos, endPos);
        if (distance < 0.001f)
            return endPos;

        Vector3 direction = (endPos - startPos).normalized;
        float radius = motor.Capsule.radius;
        float height = motor.Capsule.height;
        Vector3 upOffset = Vector3.up * (height - 2f * radius) * 0.5f;

        Vector3 bottom = startPos + Vector3.up * radius;
        Vector3 top = bottom + upOffset * 2f;

        if (Physics.CapsuleCast(
            bottom,
            top,
            radius,
            direction,
            out RaycastHit hit,
            distance,
            motor.CollidableLayers,
            QueryTriggerInteraction.Ignore))
        {
            float safeDist = Mathf.Max(0f, hit.distance - 0.01f);
            return startPos + direction * safeDist;
        }
        return endPos;
    }
    #endregion

    #region Debug
    void AddDebugPoint(Vector3 point, Color color)
    {
        if (debugPointCount < debugPoints.Length)
        {
            debugPoints[debugPointCount] = point;
            debugPointCount++;
        }
    }

    void OnDrawGizmos()
    {
        if (!Application.isPlaying) return;

        // Show debug points
        Gizmos.color = Color.yellow;
        for (int i = 0; i < debugPointCount; i++)
        {
            Gizmos.DrawWireSphere(debugPoints[i], 0.02f);
        }

        // Grab visuals
        if (isHanging || isVaulting)
        {
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(grabPoint, 0.05f);
            Gizmos.DrawRay(grabPoint, grabNormal * 0.3f);

            Gizmos.color = Color.blue;
            Gizmos.DrawWireSphere(GetHangPosition(), 0.05f);

            // Vault arc
            if (isVaulting)
            {
                Gizmos.color = Color.white;
                Vector3 prev = vaultStartPos;
                int steps = 20;
                for (int i = 1; i <= steps; i++)
                {
                    float t = i / (float)steps;
                    Vector3 pos = Vector3.Lerp(vaultStartPos, vaultEndPos, t);
                    float arcHeight = Mathf.Sin(t * Mathf.PI) * motor.Capsule.radius;
                    pos.y += arcHeight;
                    Gizmos.DrawLine(prev, pos);
                    prev = pos;
                }
            }
        }
    }
    #endregion
}