using UnityEngine;
using KinematicCharacterController;
using System.Collections.Generic;

public class InvItemDropping : MonoBehaviour
{
    [SerializeField] private GameObject itemDropPrefab; // Fallback prefab for items without WorldModelPrefab
    [SerializeField] private EquipmentManager equipmentManager;
    [SerializeField] private PersistenceManager progressionManager;

    // Optional model prefab dictionary to override item-specific models
    [SerializeField] private bool useGlobalModelOverrides = false;
    [SerializeField] private ItemModelDictionary modelOverrides;

    [Header("Object Pooling")]
    [SerializeField] private bool enableObjectPooling = true;
    [SerializeField] private int initialPoolSize = 20;
    [SerializeField] private int maxPoolSize = 100;
    
    [System.Serializable]
    public class ItemModelDictionary
    {
        [System.Serializable]
        public class ItemModelPair
        {
            public string itemName;
            public GameObject modelPrefab;
        }
        
        public ItemModelPair[] pairs;
        
        public GameObject GetModelForItem(string itemName)
        {
            if (pairs == null) return null;
            
            foreach (var pair in pairs)
            {
                if (pair.itemName == itemName)
                {
                    return pair.modelPrefab;
                }
            }
            
            return null;
        }
    }
    
    public float throwForce = 8f;
    public float throwUpwardForce = 2f;
    public float dropOffset = 1.5f;

    private Camera playerCamera;

    [Header("Passenger Zone Drop Behavior")]
	[Tooltip("Block dropping while the player is within a KinematicPlatformPassengerZone")]
    [SerializeField] private bool enablePassengerZoneOverride = true;

	[Tooltip("If inside a passenger zone, block dropping when platform speed is at or above this value (m/s)")]
	[SerializeField] private float blockDropAboveSpeed = 50f;

    // Object pooling
    private Queue<GameObject> itemPool = new Queue<GameObject>();
    private HashSet<GameObject> activeItems = new HashSet<GameObject>();

    // Pre-allocated buffer for Physics.OverlapSphereNonAlloc to avoid allocations
    private Collider[] overlapBuffer = new Collider[10];
    
    /// <summary>
    /// Get the item drop prefab for spawning items
    /// </summary>
    public GameObject GetItemDropPrefab()
    {
        return itemDropPrefab;
    }

    private void Start()
    {
        // Find the player camera reference
        if (Camera.main != null)
        {
            playerCamera = Camera.main;
        }
        else
        {
            // Try to find it by tag
            GameObject cameraObj = GameObject.FindGameObjectWithTag("MainCamera");
            if (cameraObj != null)
            {
                playerCamera = cameraObj.GetComponent<Camera>();
            }
        }

        if (playerCamera == null)
        {
            Debug.LogWarning("InvItemDropping: Could not find main camera. Using fallback for drop positions.");
        }

        // Check equipment reference
        if (equipmentManager == null)
        {
            equipmentManager = FindFirstObjectByType<EquipmentManager>();
            if (equipmentManager == null)
            {
                Debug.LogError("InvItemDropping: EquipmentManager reference is missing!");
            }
        }

        // Check progression manager
        if (progressionManager == null)
        {
            progressionManager = PersistenceManager.Instance;
            if (progressionManager == null)
            {
                Debug.LogError("InvItemDropping: PersistenceManager reference is missing!");
            }
        }

        // Initialize object pool
        if (enableObjectPooling)
        {
            InitializeObjectPool();
        }
    }

    private void InitializeObjectPool()
    {
        for (int i = 0; i < initialPoolSize; i++)
        {
            GameObject pooledItem = Instantiate(itemDropPrefab);
            pooledItem.SetActive(false);
            itemPool.Enqueue(pooledItem);
        }
        Debug.Log($"InvItemDropping: Initialized object pool with {initialPoolSize} items");
    }

    private GameObject GetPooledItem()
    {
        if (!enableObjectPooling)
            return null;

        if (itemPool.Count > 0)
        {
            GameObject pooledItem = itemPool.Dequeue();
            activeItems.Add(pooledItem);
            return pooledItem;
        }

        // Pool is empty, create new item if under max limit
        if (activeItems.Count < maxPoolSize)
        {
            GameObject newItem = Instantiate(itemDropPrefab);
            activeItems.Add(newItem);
            return newItem;
        }

        Debug.LogWarning("InvItemDropping: Object pool at maximum capacity, creating non-pooled item");
        return null;
    }

    public void ReturnToPool(GameObject item)
    {
        if (!enableObjectPooling || item == null)
            return;

        if (activeItems.Contains(item))
        {
            activeItems.Remove(item);

            // Reset the item state
            ResetPooledItem(item);

            item.SetActive(false);
            itemPool.Enqueue(item);
        }
    }

    private void ResetPooledItem(GameObject item)
    {
        // Reset rigidbody
        Rigidbody rb = item.GetComponent<Rigidbody>();
        if (rb != null)
        {
            rb.linearVelocity = Vector3.zero;
            rb.angularVelocity = Vector3.zero;
            rb.isKinematic = false;
        }

        // Reset pickup component
        InvItemPickup pickup = item.GetComponent<InvItemPickup>();
        if (pickup != null)
        {
            pickup.ResetForPool();
        }

        // Remove any storage components that might have been added
        InvDroppedStorageEquipment storage = item.GetComponent<InvDroppedStorageEquipment>();
        if (storage != null)
        {
            Destroy(storage);
        }
    }
    
    private GameObject SpawnItemPrefab(Item itemToDrop, Vector3 position, Quaternion rotation)
    {
        GameObject droppedItem;

        // Try to get from object pool first if using generic prefab
        bool useGenericPrefab = ShouldUseGenericPrefab(itemToDrop);

        if (useGenericPrefab && enableObjectPooling)
        {
            droppedItem = GetPooledItem();
            if (droppedItem != null)
            {
                droppedItem.transform.position = position;
                droppedItem.transform.rotation = rotation;
                droppedItem.SetActive(true);
            }
        }
        else
        {
            droppedItem = null;
        }

        // If we couldn't get from pool or not using generic prefab, instantiate normally
        if (droppedItem == null)
        {
            GameObject prefabToSpawn = GetPrefabToSpawn(itemToDrop);
            droppedItem = Instantiate(prefabToSpawn, position, rotation);
        }

        // Make sure it has the required components for a dropped item
        EnsureRequiredComponents(droppedItem, itemToDrop);

        return droppedItem;
    }

    private bool ShouldUseGenericPrefab(Item itemToDrop)
    {
        if (useGlobalModelOverrides && modelOverrides != null)
        {
            GameObject overrideModel = modelOverrides.GetModelForItem(itemToDrop.itemName);
            if (overrideModel != null) return false;
            if (itemToDrop.WorldModelPrefab != null) return false;
            return true; // Fallback to generic
        }
        else if (itemToDrop.WorldModelPrefab != null)
        {
            return false; // Use item's own prefab
        }
        else
        {
            return true; // Fallback to generic
        }
    }

    private GameObject GetPrefabToSpawn(Item itemToDrop)
    {
        // Check for model override
        if (useGlobalModelOverrides && modelOverrides != null)
        {
            GameObject overrideModel = modelOverrides.GetModelForItem(itemToDrop.itemName);
            if (overrideModel != null)
            {
                return overrideModel;
            }
            else if (itemToDrop.WorldModelPrefab != null)
            {
                return itemToDrop.WorldModelPrefab;
            }
            else
            {
                return itemDropPrefab;
            }
        }
        else if (itemToDrop.WorldModelPrefab != null)
        {
            return itemToDrop.WorldModelPrefab;
        }
        else
        {
            return itemDropPrefab;
        }
    }

    // Removed platform-pausing and grounded-placement helpers to keep drop logic minimal

    private bool TryGetPassengerZoneAndPlatformVelocity(out Vector3 platformVelocity)
    {
        platformVelocity = Vector3.zero;
        if (!enablePassengerZoneOverride)
        {
            return false;
        }

        // Use non-allocating version to avoid garbage collection in hot path
        int overlapCount = Physics.OverlapSphereNonAlloc(transform.position, 0.25f, overlapBuffer, ~0, QueryTriggerInteraction.Collide);
        if (overlapCount == 0)
        {
            return false;
        }

        for (int i = 0; i < overlapCount; i++)
        {
            var col = overlapBuffer[i];
            if (col == null) continue;

            var zone = col.GetComponentInParent<KinematicPlatformPassengerZone>();
            if (zone == null) continue;

            // Find platform mover to read velocity
            var platform = zone.GetComponentInParent<KinematicPlatform>();
            if (platform != null)
            {
                var mover = platform.GetComponent<PhysicsMover>();
                if (mover != null)
                {
                    platformVelocity = mover.Velocity;
                    return true;
                }
            }

            // Fallback: try rigidbody velocity on platform
            var rb = zone.GetComponentInParent<Rigidbody>();
            if (rb != null)
            {
                platformVelocity = rb.linearVelocity;
                return true;
            }
        }

        return false;
    }

	public bool IsDropBlockedInPassengerZone(out string reason)
    {
        reason = null;
        if (!enablePassengerZoneOverride)
        {
            return false;
        }
		Vector3 platformVelocity;
		bool inZone = TryGetPassengerZoneAndPlatformVelocity(out platformVelocity);
		if (!inZone)
		{
			return false;
		}
		// Allow drop when platform speed is below threshold; block otherwise
		float speed = platformVelocity.magnitude;
		if (speed >= blockDropAboveSpeed)
		{
			reason = "Can't drop right now";
			return true;
		}
		return false;
    }
    
    private void EnsureRequiredComponents(GameObject droppedItem, Item item)
    {
        // Make sure it has a Rigidbody
        Rigidbody rb = droppedItem.GetComponent<Rigidbody>();
        if (rb == null)
        {
            rb = droppedItem.AddComponent<Rigidbody>();
            
            // Use default physics settings - the prefab should have proper physics already
            rb.mass = 1f;
            rb.linearDamping = 0.5f;
            rb.angularDamping = 0.2f;
            rb.collisionDetectionMode = CollisionDetectionMode.Continuous;
            rb.isKinematic = false;
            rb.useGravity = true;
        }
        
        // Make sure it has a collider
        Collider collider = droppedItem.GetComponent<Collider>();
        if (collider == null)
        {
            // Check if any child has a collider
            Collider[] childColliders = droppedItem.GetComponentsInChildren<Collider>();
            if (childColliders.Length == 0)
            {
                // Add a box collider as fallback
                BoxCollider boxCollider = droppedItem.AddComponent<BoxCollider>();
                boxCollider.size = new Vector3(0.5f, 0.5f, 0.5f);
                boxCollider.center = Vector3.zero;
            }
        }
        
        // Make sure it has InvItemPickup component
        InvItemPickup itemPickup = droppedItem.GetComponent<InvItemPickup>();
        if (itemPickup == null)
        {
            itemPickup = droppedItem.AddComponent<InvItemPickup>();
        }
    }
    
    public void ThrowItem(Item itemToBeThrown, int quantity = 1, bool isEquipment = false)
    {
        string reason;
        if (IsDropBlockedInPassengerZone(out reason))
        {
            Debug.Log(reason ?? "Drop blocked");
            return;
        }

        GameObject playerObject = gameObject;
        Vector3 dropPosition;
        Vector3 dropDirection;

        if (playerCamera != null)
        {
            dropPosition = playerCamera.transform.position + playerCamera.transform.forward * dropOffset;
            dropDirection = playerCamera.transform.forward;
        }
        else
        {
            dropPosition = transform.position + transform.forward * dropOffset;
            dropPosition.y += 1.5f;
            dropDirection = transform.forward;
        }

        GameObject droppedItemInstance = SpawnItemPrefab(itemToBeThrown, dropPosition, Quaternion.identity);
        InvItemPickup invItemPickup = droppedItemInstance.GetComponent<InvItemPickup>();

        if (invItemPickup != null)
        {
            invItemPickup.SetItem(itemToBeThrown, quantity);

            if (isEquipment && itemToBeThrown is Bag)
            {
                var droppedStorage = droppedItemInstance.GetComponent<InvDroppedStorageEquipment>();
                if (droppedStorage == null)
                {
                    droppedStorage = droppedItemInstance.AddComponent<InvDroppedStorageEquipment>();
                }
            }

            invItemPickup.MarkAsPlayerDropped();

            Rigidbody rb = droppedItemInstance.GetComponent<Rigidbody>();
            if (rb != null)
            {
                Vector3 force = dropDirection * throwForce + Vector3.up * throwUpwardForce;
                Rigidbody playerRb = playerObject.GetComponent<Rigidbody>();
                if (playerRb != null)
                {
                    force += playerRb.linearVelocity * 0.5f;
                }
                rb.isKinematic = false;
                rb.linearVelocity = Vector3.zero;
                rb.AddForce(force, ForceMode.Impulse);
                rb.AddTorque(new Vector3(
                    Random.Range(-1f, 1f),
                    Random.Range(-1f, 1f),
                    Random.Range(-1f, 1f)
                ).normalized * 2f, ForceMode.Impulse);
            }
        }
        else
        {
            Debug.LogError("InvItemPickup component not found on the spawned prefab.");
            Destroy(droppedItemInstance);
        }
    }

    public GameObject DropItem(Item itemToDrop, int quantity, bool isEquipment = false)
    {
        if (itemToDrop == null)
        {
            Debug.LogError("Attempted to drop null item");
            return null;
        }
        
        bool isStorageItem = isEquipment && itemToDrop is Bag;
        EquipmentBase itemToBeDropped = isEquipment ? (EquipmentBase)itemToDrop : null;
        
        string reason;
        if (IsDropBlockedInPassengerZone(out reason))
        {
            Debug.Log(reason ?? "Drop blocked");
            return null;
        }

        Vector3 dropPosition;
        Vector3 dropDirection;
        if (playerCamera != null)
        {
            dropPosition = playerCamera.transform.position + playerCamera.transform.forward * dropOffset;
            dropDirection = playerCamera.transform.forward;
        }
        else
        {
            dropPosition = transform.position + transform.forward * dropOffset;
            dropPosition.y += 1.5f;
            dropDirection = transform.forward;
        }
        
        // Handle any serialization needed for storage items
        string serializedContent = "";
        if (isStorageItem)
        {
            // If dropping a storage item, we need to handle serializing its content
            EquipmentSlotType slotType = itemToBeDropped.Slot;
            
            // Only serialize if it's currently equipped
            var slot = equipmentManager.GetEquipmentSlot(slotType);
            if (slot?.equippedItem != null && slot.equippedItem == itemToBeDropped)
            {
                serializedContent = equipmentManager.SerializeContainerContent(slotType);
            }
            
            // Unequip the item if it's equipped
            if (slot?.equippedItem == itemToBeDropped)
            {
                equipmentManager.UnequipItem(slotType, true);
            }
        }
        
        // Spawn the actual item prefab
        GameObject droppedObject = SpawnItemPrefab(itemToDrop, dropPosition, Quaternion.identity);
        InvItemPickup droppedItemComponent = droppedObject.GetComponent<InvItemPickup>();
        
        if (droppedItemComponent != null)
        {
            droppedItemComponent.SetItem(itemToDrop, quantity);
            
            // If it's a storage item with content
            if (isStorageItem && !string.IsNullOrEmpty(serializedContent))
            {
                var storageComponent = droppedObject.GetComponent<InvDroppedStorageEquipment>();
                if (storageComponent == null)
                {
                    storageComponent = droppedObject.AddComponent<InvDroppedStorageEquipment>();
                }
                storageComponent.StorageContent = serializedContent;
            }
            
            Rigidbody rb = droppedObject.GetComponent<Rigidbody>();
            if (rb != null)
            {
                Vector3 force = dropDirection * throwForce + Vector3.up * throwUpwardForce;
                rb.isKinematic = false;
                rb.linearVelocity = Vector3.zero;
                rb.AddForce(force, ForceMode.Impulse);
                rb.AddTorque(new Vector3(
                    Random.Range(-1f, 1f),
                    Random.Range(-1f, 1f),
                    Random.Range(-1f, 1f)
                ).normalized * 2f, ForceMode.Impulse);
            }
            
            // Mark as player-dropped for persistence
            droppedItemComponent.MarkAsPlayerDropped();
            
            return droppedObject;
        }
        else
        {
            Debug.LogError("InvItemPickup component not found on spawned item prefab.");
            Destroy(droppedObject);
            return null;
        }
    }

    /// <summary>
    /// Creates a dropped item instance in the world from a string item name.
    /// Used by UI systems like InvDragAndDropManager for drag-drop operations.
    /// </summary>
    public void CreateDroppedItemInstance(
        GameObject playerObject,
        string itemName,
        Vector2 screenPosition,
        bool isEquipment,
        int equipmentSlotTypeInt,
        string serializedContainerContent,
        int quantity = 1)
    {
        // Validate item name
        if (string.IsNullOrEmpty(itemName))
        {
            Debug.LogError("Item name is null or empty. Cannot drop item.");
            return;
        }

        // Get the item data
        Item itemToBeDropped = ItemDatabase.GetItemByName(itemName);
        if (itemToBeDropped == null)
        {
            Debug.LogError($"Item with name '{itemName}' not found in database. Cannot drop item.");
            return;
        }
        
        string reason;
        if (IsDropBlockedInPassengerZone(out reason))
        {
            Debug.Log(reason ?? "Drop blocked");
            return;
        }

        Vector3 dropPosition;
        Vector3 dropDirection;
        if (playerCamera != null)
        {
            dropPosition = playerCamera.transform.position + playerCamera.transform.forward * dropOffset;
            dropDirection = playerCamera.transform.forward;
        }
        else
        {
            dropPosition = playerObject.transform.position + playerObject.transform.forward * dropOffset;
            dropPosition.y += 1.5f;
            dropDirection = playerObject.transform.forward;
        }

        // Spawn the item prefab
        GameObject droppedItemInstance = SpawnItemPrefab(itemToBeDropped, dropPosition, Quaternion.identity);
        InvItemPickup invItemPickup = droppedItemInstance.GetComponent<InvItemPickup>();
        
        if (invItemPickup != null)
        {
            invItemPickup.SetItem(itemToBeDropped, quantity);
            
            // Removed SetTimeSinceThrown - no longer needed for 3D game
            
            // Handle storage equipment if needed
            if (isEquipment && itemToBeDropped is Bag)
            {
                var droppedStorage = droppedItemInstance.GetComponent<InvDroppedStorageEquipment>();
                if (droppedStorage == null)
                {
                    droppedStorage = droppedItemInstance.AddComponent<InvDroppedStorageEquipment>();
                }
                
                droppedStorage.SetContainerSnapshot(serializedContainerContent);
            }
            
            // Mark as player-dropped for persistence
            invItemPickup.MarkAsPlayerDropped();
            
            Rigidbody rb = droppedItemInstance.GetComponent<Rigidbody>();
            if (rb != null)
            {
                Vector3 force = dropDirection * throwForce + Vector3.up * throwUpwardForce;
                Rigidbody playerRb = playerObject.GetComponent<Rigidbody>();
                if (playerRb != null)
                {
                    force += playerRb.linearVelocity * 0.5f;
                }
                rb.isKinematic = false;
                rb.linearVelocity = Vector3.zero;
                rb.AddForce(force, ForceMode.Impulse);
                rb.AddTorque(new Vector3(
                    Random.Range(-1f, 1f),
                    Random.Range(-1f, 1f),
                    Random.Range(-1f, 1f)
                ).normalized * 2f, ForceMode.Impulse);
            }
        }
        else
        {
            Debug.LogError("InvItemPickup component not found on the spawned prefab.");
            Destroy(droppedItemInstance);
        }
    }
}