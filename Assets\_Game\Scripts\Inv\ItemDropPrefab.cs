using UnityEngine;

// This script is attached to the item drop prefab
public class ItemDropPrefab : MonoBehaviour
{
    [SerializeField] private MeshRenderer meshRenderer;
    [SerializeField] private Collider itemCollider;
    [SerializeField] private Rigidbody itemRigidbody;
    [SerializeField] private Transform modelParent; // Optional parent transform for models
    
    private void Awake()
    {
        // Auto-find components if not set
        if (meshRenderer == null)
            meshRenderer = GetComponentInChildren<MeshRenderer>();
        
        if (itemCollider == null)
            itemCollider = GetComponent<Collider>();
        
        if (itemRigidbody == null)
            itemRigidbody = GetComponent<Rigidbody>();
        
        // Add components if missing
        if (itemRigidbody == null)
            itemRigidbody = gameObject.AddComponent<Rigidbody>();
            
        if (itemCollider == null)
            itemCollider = gameObject.AddComponent<BoxCollider>();
        
        // Make sure we have an InvItemPickup component
        if (GetComponent<InvItemPickup>() == null)
            gameObject.AddComponent<InvItemPickup>();
        
        // Model swapper components are no longer used - removed from system
    }
    
    private void Start()
    {
        // Configure rigidbody
        if (itemRigidbody != null)
        {
            itemRigidbody.mass = 1f;
            itemRigidbody.linearDamping = 0.5f;
            itemRigidbody.angularDamping = 0.2f;
            itemRigidbody.collisionDetectionMode = CollisionDetectionMode.Continuous;
        }
        
        // Configure collider
        if (itemCollider != null && itemCollider is BoxCollider boxCollider)
        {
            boxCollider.size = new Vector3(0.5f, 0.5f, 0.5f);
            boxCollider.center = Vector3.zero;
            boxCollider.isTrigger = false;
        }
    }
} 