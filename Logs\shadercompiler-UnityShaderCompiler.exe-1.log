Base path: 'C:/Unity/Editors/6000.2.0f1/Editor/Data', plugins path 'C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines'
Cmd: initializeCompiler

Cmd: compileSnippet
  insize=18200 file=Packages/com.unity.render-pipelines.high-definition/Runtime/Material/Lit/Lit.shader name=HDRP/Lit pass=Forward ppOnly=0 stripLineD=0 buildPlatform=19 rsLen=0 pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=DOTS_INSTANCING_ON _MAPPING_TRIPLANAR USE_LEGACY_LIGHTMAPS PROBE_VOLUMES_L1 DECALS_4RT _NORMALMAP_TANGENT_SPACE _DISABLE_SSR_TRANSPARENT SCREEN_SPACE_SHADOWS_OFF PUNCTUAL_SHADOW_HIGH DIRECTIONAL_SHADOW_HIGH AREA_SHADOW_MEDIUM USE_FPTL_LIGHTLIST dKW=SHADOWS_SHADOWMASK PROBE_VOLUMES_L2 SCREEN_SPACE_SHADOWS_ON DECALS_OFF DECALS_3RT DECAL_SURFACE_GRADIENT PUNCTUAL_SHADOW_LOW PUNCTUAL_SHADOW_MEDIUM DIRECTIONAL_SHADOW_LOW DIRECTIONAL_SHADOW_MEDIUM AREA_SHADOW_HIGH USE_CLUSTERED_LIGHTLIST LOD_FADE_CROSSFADE DEBUG_DISPLAY LIGHTMAP_ON LIGHTMAP_BICUBIC_SAMPLING DIRLIGHTMAP_COMBINED DYNAMICLIGHTMAP_ON _SURFACE_TYPE_TRANSPARENT INSTANCING_ON _PIXEL_DISPLACEMENT_LOCK_OBJECT_SCALE _EMISSIVE_MAPPING_PLANAR _EMISSIVE_MAPPING_TRIPLANAR _EMISSIVE_MAPPING_BASE _ENABLESPECULAROCCLUSION _SPECULAR_OCCLUSION_NONE _SPECULAR_OCCLUSION_FROM_BENT_NORMAL_MAP _MATERIAL_FEATURE_CLEAR_COAT _DISABLE_SSR _MATERIAL_FEATURE_SUBSURFACE_SCATTERING _MATERIAL_FEATURE_TRANSMISSION _MATERIAL_FEATURE_ANISOTROPY _MATERIAL_FEATURE_IRIDESCENCE _MATERIAL_FEATURE_SPECULAR_COLOR _ENABLE_GEOMETRIC_SPECULAR_AA _BENTNORMALMAP _EMISSIVE_COLOR_MAP _TANGENTMAP _ANISOTROPYMAP _DETAIL_MAP _SUBSURFACE_MASK_MAP _TRANSMISSION_MASK_MAP _THICKNESSMAP _IRIDESCENCE_THICKNESSMAP _SPECULARCOLORMAP _TRANSMITTANCECOLORMAP _MASKMAP _ENABLE_FOG_ON_TRANSPARENT _REFRACTION_PLANE _REFRACTION_SPHERE _REFRACTION_THIN _DEPTHOFFSET_ON _DOUBLESIDED_ON _VERTEX_DISPLACEMENT _PIXEL_DISPLACEMENT _DISPLACEMENT_LOCK_TILING_SCALE _MAPPING_PLANAR _REQUIRE_UV2 _REQUIRE_UV3 _HEIGHTMAP _DISABLE_DECALS _NORMALMAP _ALPHATEST_ON _TRANSPARENT_WRITES_MOTION_VEC _TRANSPARENT_REFRACTIVE_SORT _ADD_PRECOMPUTED_VELOCITY UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 UNITY_PBS_USE_BRDF3 UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_RGBM_ENCODING UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=0 lang=3 type=Fragment platform=d3d11 reqs=1101803 mask=6 start=980 ok=1 outsize=105986

