using UnityEngine;
using System.Collections;
using System.Collections.Generic;

[DefaultExecutionOrder(-100)]
[DisallowMultipleComponent]
public class HeavyObjectManager : MonoBehaviour, IRecoverable
{
    [Header("Heavy Object Settings")]
    [SerializeField] private string objectName = "";
    [SerializeField] private bool savePhysicsState = false;
    [SerializeField] private bool isMovable = true;
    [SerializeField] private string category = "Props";

    [Header("Physics Properties")]
    [SerializeField] private float mass = 50f; // Mass in kg for damage calculations
    [SerializeField] private bool autoDetectMass = true; // Auto-detect from Rigidbody
    [SerializeField] private float massGrabThreshold = 100f; // Below this mass, use normal grab

    [Header("Collision Damage Settings")]
    [SerializeField] private bool canDamagePlayer = true;
    [SerializeField] private float minImpactVelocity = 5f; // Minimum velocity to cause damage
    [SerializeField] private float damagePerMomentum = 0.1f; // Damage = momentum * this value (reduced from 0.5)
    [SerializeField] private float lethalMomentumThreshold = 2000f; // Momentum that causes instant death (increased from 500)
    [SerializeField] private float ragdollMomentumThreshold = 150f; // Momentum that triggers ragdoll (reduced from 300)
    [SerializeField] private float pushOnlyMomentumThreshold = 75f; // Below ragdoll, just push the player
    [SerializeField] private float impactForceMultiplier = 3f; // Force applied to player when hit (increased)
    [SerializeField] private float minDamage = 5f; // Minimum damage if above velocity threshold
    [SerializeField] private float maxNonLethalDamage = 80f; // Cap damage to prevent instant death

    [Header("Impact Effects")]
    [SerializeField] private bool enableImpactShake = true;
    [SerializeField] private float minShakeVelocity = 3f; // Minimum velocity for screen shake
    [SerializeField] private float shakeIntensityMultiplier = 0.01f; // Shake = mass * velocity * this
    [SerializeField] private float shakeFrequency = 20f; // Frequency of the shake
    [SerializeField] private float shakeDuration = 0.5f; // How long the shake lasts
    [SerializeField] private float maxShakeIntensity = 1f; // Maximum shake intensity cap
    [SerializeField] private float shakeRadius = 30f; // Max distance for shake effect
    [SerializeField] private float groundImpactMultiplier = 1.5f; // Extra shake when hitting ground

    [Header("State Tracking (Read Only)")]
    [SerializeField] private string uniqueId = "";
    [SerializeField] private Vector3 initialPosition = Vector3.zero;
    [SerializeField] private Quaternion initialRotation = Quaternion.identity;
    [SerializeField] private bool hasBeenMoved = false;
    [SerializeField] private bool isCurrentlyMoving = false;
    [SerializeField] private float lastMovementTime = 0f;

    [Header("Recovery Settings")]
    [SerializeField] private bool enableRecovery = true;
    [SerializeField] private Transform recoveryPoint;
    [SerializeField] private Vector3 recoveryOffset = Vector3.zero;
    [SerializeField] private float recoveryHeight = 2f;

    // Movement detection
    private Vector3 lastKnownPosition;
    private Quaternion lastKnownRotation;
    private const float MOVEMENT_THRESHOLD_SQR = 0.0001f;
    private const float ROTATION_THRESHOLD = 0.1f;
    private const float VELOCITY_THRESHOLD = 0.01f;
    private const float MOVEMENT_STOP_TIME = 0.5f;

    // Collision tracking
    private Vector3 lastVelocity;
    private float timeSinceLastImpact = 0f;
    private const float IMPACT_COOLDOWN = 0.2f; // Prevent multiple impacts in quick succession

    // Cached components
    private Rigidbody rb;
    private bool isRegistered = false;
    private Coroutine updateCoroutine;
    private HeadBob playerHeadBob;
    private FallDamageSystem playerFallDamage;

    // Static registry
    private static Dictionary<string, HeavyObjectManager> allHeavyObjects = new Dictionary<string, HeavyObjectManager>(1000);
    private static List<HeavyObjectManager> activeMovingObjects = new List<HeavyObjectManager>(10);

    // Properties
    public string UniqueId => uniqueId;
    public string ObjectName => string.IsNullOrEmpty(objectName) ? gameObject.name : objectName;
    public Vector3 InitialPosition => initialPosition;
    public Quaternion InitialRotation => initialRotation;
    public bool HasBeenMoved => hasBeenMoved;
    public bool IsMovable => isMovable;
    public bool IsCurrentlyMoving => isCurrentlyMoving;
    public string Category => category;
    public float Mass => mass;
    public bool IsLightEnoughForNormalGrab => mass < massGrabThreshold;

    // IRecoverable implementation
    public string RecoveryName => ObjectName;
    public Vector3 GetPosition() => transform.position;
    public bool IsValid() => this != null && gameObject != null;

    public void RecoverToPosition(Vector3 position)
    {
        transform.position = position;
        transform.rotation = initialRotation;

        if (rb != null)
        {
            rb.linearVelocity = Vector3.zero;
            rb.angularVelocity = Vector3.zero;
        }

        hasBeenMoved = true;
        SetMovingState(false);
        NotifyPersistenceManagerOfMovement();
    }

    private void Awake()
    {
        rb = GetComponent<Rigidbody>();

        // Auto-detect mass from Rigidbody if enabled
        if (autoDetectMass && rb != null)
        {
            mass = rb.mass;
        }
        else if (rb != null && !autoDetectMass)
        {
            // Set Rigidbody mass to match our configured mass
            rb.mass = mass;
        }

        if (initialPosition == Vector3.zero && initialRotation == Quaternion.identity)
        {
            initialPosition = transform.position;
            initialRotation = transform.rotation;
        }

        if (string.IsNullOrEmpty(uniqueId))
        {
            GenerateUniqueId();
        }

        lastKnownPosition = transform.position;
        lastKnownRotation = transform.rotation;
        lastVelocity = rb != null ? rb.linearVelocity : Vector3.zero;

        RegisterInStaticDictionary();
    }

    private void Start()
    {
        RegisterWithPersistenceManager();

        // Find player components for damage and shake
        GameObject player = GameObject.FindGameObjectWithTag("Player");
        if (player != null)
        {
            playerHeadBob = player.GetComponentInChildren<HeadBob>();
            playerFallDamage = player.GetComponent<FallDamageSystem>();
        }

        // Register with recovery manager if enabled
        if (enableRecovery && ObjectRecoveryManager.Instance != null)
        {
            ObjectRecoveryManager.Instance.RegisterObject(this);
        }

        StartCoroutine(BeginMonitoringWhenReady());
    }

    private void FixedUpdate()
    {
        // Track velocity for collision detection
        if (rb != null)
        {
            lastVelocity = rb.linearVelocity;
        }

        // Update impact cooldown
        if (timeSinceLastImpact < IMPACT_COOLDOWN)
        {
            timeSinceLastImpact += Time.fixedDeltaTime;
        }
    }

    private void OnCollisionEnter(Collision collision)
    {
        HandleCollisionImpact(collision);
    }

    private void HandleCollisionImpact(Collision collision)
    {
        // Skip if on cooldown
        if (timeSinceLastImpact < IMPACT_COOLDOWN)
            return;

        float impactVelocity = collision.relativeVelocity.magnitude;
        
        // Check if this is a player collision
        bool hitPlayer = collision.gameObject.CompareTag("Player");
        
        if (hitPlayer && canDamagePlayer && impactVelocity > minImpactVelocity)
        {
            HandlePlayerImpact(collision, impactVelocity);
        }
        
        // Handle screen shake for any significant impact
        if (enableImpactShake && impactVelocity > minShakeVelocity)
        {
            HandleImpactShake(collision, impactVelocity, hitPlayer);
        }

        timeSinceLastImpact = 0f;
    }

    private void HandlePlayerImpact(Collision collision, float impactVelocity)
    {
        // Calculate momentum-based impact severity
        float momentum = mass * impactVelocity;

        Debug.Log($"[HeavyObjectManager] {ObjectName} hit player - Mass: {mass}kg, Velocity: {impactVelocity}m/s, Momentum: {momentum}");

        // Get player components
        GameObject player = collision.gameObject;
        Rigidbody playerRb = player.GetComponent<Rigidbody>();

        // Calculate impact direction for knockback
        Vector3 impactDirection = (player.transform.position - transform.position).normalized;
        impactDirection.y = Mathf.Max(0.2f, impactDirection.y); // Ensure some upward component

        // Apply knockback based on momentum (but never lethal)
        if (playerRb != null)
        {
            float knockbackMultiplier = impactForceMultiplier;

            // Scale knockback based on momentum thresholds
            if (momentum >= ragdollMomentumThreshold)
            {
                knockbackMultiplier *= 1.5f; // Stronger knockback for heavy impacts
                Debug.Log($"[HeavyObjectManager] Heavy impact! Momentum {momentum} - applying strong knockback");
            }
            else if (momentum >= pushOnlyMomentumThreshold)
            {
                knockbackMultiplier *= 0.7f; // Moderate knockback
                Debug.Log($"[HeavyObjectManager] Moderate impact! Momentum {momentum} - applying moderate knockback");
            }
            else
            {
                knockbackMultiplier *= 0.3f; // Light knockback
                Debug.Log($"[HeavyObjectManager] Light impact! Momentum {momentum} - applying light knockback");
            }

            Vector3 knockbackForce = impactDirection * momentum * knockbackMultiplier;
            playerRb.AddForce(knockbackForce, ForceMode.Impulse);
        }

        // Use FallDamageSystem for energy-based damage (never lethal)
        if (playerFallDamage != null && momentum >= pushOnlyMomentumThreshold)
        {
            // Convert momentum to equivalent fall parameters for the energy system
            // Higher momentum = higher simulated fall height and velocity
            float simulatedFallHeight = Mathf.Clamp(momentum / 30f, 2f, 12f); // Scale momentum to fall height (2-12m range)
            float simulatedFallVelocity = Mathf.Clamp(impactVelocity * 0.8f, 5f, 15f); // Scale impact velocity (5-15 m/s range, below lethal)

            Debug.Log($"[HeavyObjectManager] Using FallDamageSystem - Simulated height: {simulatedFallHeight}m, velocity: {simulatedFallVelocity}m/s");

            // Let the FallDamageSystem handle energy consumption and potential tumble
            // This will never kill the player, only consume energy and potentially cause tumble
            playerFallDamage.ForceApplyFallDamage(simulatedFallHeight, simulatedFallVelocity);
        }
        else if (momentum < pushOnlyMomentumThreshold)
        {
            Debug.Log($"[HeavyObjectManager] Impact too light for energy drain - only knockback applied");
        }
    }

    private void HandleImpactShake(Collision collision, float impactVelocity, bool hitPlayer)
    {
        // Calculate base shake intensity based on mass and velocity
        float baseIntensity = mass * impactVelocity * shakeIntensityMultiplier;
        
        // Extra intensity for ground impacts
        bool isGroundImpact = Vector3.Dot(collision.GetContact(0).normal, Vector3.up) > 0.5f;
        if (isGroundImpact)
        {
            baseIntensity *= groundImpactMultiplier;
        }
        
        // Cap the intensity
        baseIntensity = Mathf.Min(baseIntensity, maxShakeIntensity);
        
        Debug.Log($"[HeavyObjectManager] Impact shake - Base intensity: {baseIntensity}, Mass: {mass}kg, Velocity: {impactVelocity}m/s");
        
        // Find all nearby players to shake their screens
        GameObject[] players = GameObject.FindGameObjectsWithTag("Player");
        
        foreach (GameObject player in players)
        {
            float distance = Vector3.Distance(transform.position, player.transform.position);
            
            // Apply distance falloff with smooth curve
            if (distance <= shakeRadius)
            {
                // Use inverse square falloff for more realistic shake propagation
                float distanceFactor = 1f - Mathf.Pow(distance / shakeRadius, 2f);
                float finalIntensity = baseIntensity * distanceFactor;
                
                // Extra intensity if this player was directly hit
                if (hitPlayer && player == collision.gameObject)
                {
                    finalIntensity = Mathf.Min(finalIntensity * 2f, maxShakeIntensity);
                }
                
                // Only apply shake if intensity is significant
                if (finalIntensity > 0.01f)
                {
                    // Apply screen shake
                    HeadBob headBob = player.GetComponentInChildren<HeadBob>();
                    if (headBob != null)
                    {
                        // Calculate position and roll based on final intensity
                        float posAmplitude = finalIntensity * 0.1f; // Scale position shake
                        float rollAmplitude = finalIntensity * 15f; // Scale roll shake
                        
                        // Apply the shake with full intensity first
                        headBob.SetExternalBoostShake(
                            finalIntensity,
                            shakeFrequency,
                            posAmplitude,
                            rollAmplitude
                        );
                        
                        // Start coroutine to maintain then fade the shake
                        StartCoroutine(MaintainThenFadeShake(headBob, finalIntensity, posAmplitude, rollAmplitude, shakeDuration));
                        
                        Debug.Log($"[HeavyObjectManager] Applied shake to player - Distance: {distance:F1}m, Final intensity: {finalIntensity:F3}, Pos amp: {posAmplitude:F3}, Roll amp: {rollAmplitude:F1}°");
                    }
                }
            }
        }
    }

    private IEnumerator MaintainThenFadeShake(HeadBob headBob, float intensity, float posAmp, float rollAmp, float duration)
    {
        if (headBob == null) yield break;
        
        // Maintain full shake for the first 20% of duration
        float maintainTime = duration * 0.2f;
        yield return new WaitForSeconds(maintainTime);
        
        // Then fade out over the remaining 80% of duration
        float fadeTime = duration * 0.8f;
        float elapsed = 0f;
        
        while (elapsed < fadeTime)
        {
            elapsed += Time.deltaTime;
            float t = elapsed / fadeTime;
            
            // Use an easing curve for smooth fade out
            float fadeMultiplier = 1f - Mathf.Pow(t, 2f); // Quadratic ease out
            
            // Apply the fading shake
            headBob.SetExternalBoostShake(
                intensity * fadeMultiplier,
                shakeFrequency,
                posAmp * fadeMultiplier,
                rollAmp * fadeMultiplier
            );
            
            yield return null;
        }
        
        // Ensure shake is completely stopped
        headBob.SetExternalBoostShake(0f, 0f, 0f, 0f);
    }

    private void OnDestroy()
    {
        if (isCurrentlyMoving || hasBeenMoved)
        {
            NotifyPersistenceManagerOfMovement();
        }

        SetMovingState(false);
        UnregisterFromPersistenceManager();

        // Unregister from recovery manager
        if (ObjectRecoveryManager.Instance != null)
        {
            ObjectRecoveryManager.Instance.UnregisterObject(this);
        }

        UnregisterFromStaticDictionary();
    }

    private IEnumerator PhysicsMonitorLoop()
    {
        const float VELOCITY_SAVE_INTERVAL = 2f;
        float timeSinceLastVelocitySave = 0f;

        while (isMovable && rb != null)
        {
            bool isMovingNow = rb.linearVelocity.sqrMagnitude > VELOCITY_THRESHOLD ||
                              rb.angularVelocity.sqrMagnitude > VELOCITY_THRESHOLD;

            if (!isMovingNow)
            {
                Vector3 positionDelta = transform.position - lastKnownPosition;
                float rotationDelta = Quaternion.Angle(transform.rotation, lastKnownRotation);
                isMovingNow = positionDelta.sqrMagnitude > MOVEMENT_THRESHOLD_SQR ||
                             rotationDelta > ROTATION_THRESHOLD;
            }

            // Notify recovery manager of movement state changes
            if (isMovingNow && !isCurrentlyMoving)
            {
                SetMovingState(true);
                hasBeenMoved = true;
                lastMovementTime = Time.time;
                NotifyPersistenceManagerOfMovement();

                // Tell recovery manager we're moving
                if (ObjectRecoveryManager.Instance != null)
                {
                    ObjectRecoveryManager.Instance.NotifyObjectMoving(this);
                }
            }
            else if (!isMovingNow && isCurrentlyMoving)
            {
                if (Time.time - lastMovementTime > MOVEMENT_STOP_TIME)
                {
                    SetMovingState(false);
                    NotifyPersistenceManagerOfMovement();

                    // Tell recovery manager we stopped
                    if (ObjectRecoveryManager.Instance != null)
                    {
                        ObjectRecoveryManager.Instance.NotifyObjectStopped(this);
                    }
                }
            }
            else if (isMovingNow)
            {
                lastMovementTime = Time.time;
                hasBeenMoved = true;

                timeSinceLastVelocitySave += Time.fixedDeltaTime;
                if (timeSinceLastVelocitySave >= VELOCITY_SAVE_INTERVAL)
                {
                    NotifyPersistenceManagerOfMovement();
                    timeSinceLastVelocitySave = 0f;
                }
            }

            lastKnownPosition = transform.position;
            lastKnownRotation = transform.rotation;

            yield return new WaitForSeconds(0.25f);
        }
    }

    private IEnumerator TransformMonitorLoop()
    {
        while (isMovable)
        {
            Vector3 positionDelta = transform.position - lastKnownPosition;
            float rotationDelta = Quaternion.Angle(transform.rotation, lastKnownRotation);

            bool isMovingNow = positionDelta.sqrMagnitude > MOVEMENT_THRESHOLD_SQR ||
                              rotationDelta > ROTATION_THRESHOLD;

            if (isMovingNow && !isCurrentlyMoving)
            {
                SetMovingState(true);
                hasBeenMoved = true;
                lastMovementTime = Time.time;

                if (ObjectRecoveryManager.Instance != null)
                {
                    ObjectRecoveryManager.Instance.NotifyObjectMoving(this);
                }
            }
            else if (!isMovingNow && isCurrentlyMoving)
            {
                if (Time.time - lastMovementTime > MOVEMENT_STOP_TIME)
                {
                    SetMovingState(false);
                    NotifyPersistenceManagerOfMovement();

                    if (ObjectRecoveryManager.Instance != null)
                    {
                        ObjectRecoveryManager.Instance.NotifyObjectStopped(this);
                    }
                }
            }
            else if (isMovingNow)
            {
                lastMovementTime = Time.time;
                hasBeenMoved = true;
                NotifyPersistenceManagerOfMovement();
            }

            lastKnownPosition = transform.position;
            lastKnownRotation = transform.rotation;

            float checkInterval = isCurrentlyMoving ? 0.1f : 1f;
            yield return new WaitForSeconds(checkInterval);
        }
    }

    private IEnumerator BeginMonitoringWhenReady()
    {
        while (PersistenceManager.Instance == null)
        {
            yield return null;
        }
        while (PersistenceManager.IsRestoring)
        {
            yield return null;
        }

        if (!isMovable) yield break;

        if (rb != null)
        {
            StartCoroutine(PhysicsMonitorLoop());
        }
        else
        {
            updateCoroutine = StartCoroutine(TransformMonitorLoop());
        }
    }

    private void SetMovingState(bool moving)
    {
        if (isCurrentlyMoving == moving) return;

        isCurrentlyMoving = moving;

        if (moving)
        {
            if (!activeMovingObjects.Contains(this))
            {
                activeMovingObjects.Add(this);
            }
        }
        else
        {
            activeMovingObjects.Remove(this);
        }
    }

    private void GenerateUniqueId()
    {
        string sceneName = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;
        string objectId = ObjectName.Replace("(Clone)", "").Trim();

        Vector3 pos = initialPosition;
        int posX = Mathf.RoundToInt(pos.x * 100);
        int posY = Mathf.RoundToInt(pos.y * 100);
        int posZ = Mathf.RoundToInt(pos.z * 100);

        uniqueId = $"HO_{sceneName}_{objectId}_{posX}_{posY}_{posZ}";
    }

    private void RegisterWithPersistenceManager()
    {
        if (isRegistered) return;

        var persistenceManager = PersistenceManager.Instance;
        if (persistenceManager != null)
        {
            persistenceManager.RegisterHeavyObject(this);
            isRegistered = true;
        }
    }

    private void UnregisterFromPersistenceManager()
    {
        if (!isRegistered) return;

        var persistenceManager = PersistenceManager.Instance;
        if (persistenceManager != null)
        {
            persistenceManager.UnregisterHeavyObject(this);
            isRegistered = false;
        }
    }

    private void NotifyPersistenceManagerOfMovement()
    {
        if (!isRegistered) return;

        var persistenceManager = PersistenceManager.Instance;
        if (persistenceManager != null)
        {
            persistenceManager.OnHeavyObjectMoved(this);
        }
    }

    private void RegisterInStaticDictionary()
    {
        if (!allHeavyObjects.ContainsKey(uniqueId))
        {
            allHeavyObjects[uniqueId] = this;
        }
        else
        {
            uniqueId = $"{uniqueId}_{GetInstanceID()}";
            allHeavyObjects[uniqueId] = this;
        }
    }

    private void UnregisterFromStaticDictionary()
    {
        allHeavyObjects.Remove(uniqueId);
    }

    public HeavyObjectData GetSaveData()
    {
        var data = new HeavyObjectData
        {
            id = uniqueId,
            objectName = ObjectName,
            category = category,
            position = transform.position,
            rotation = transform.rotation,
            scale = transform.localScale,
            hasBeenMoved = hasBeenMoved,
            isActive = gameObject.activeSelf,
            isCurrentlyMoving = isCurrentlyMoving
        };

        if (rb != null && (isCurrentlyMoving || hasBeenMoved || savePhysicsState))
        {
            data.velocity = rb.linearVelocity;
            data.angularVelocity = rb.angularVelocity;
        }

        return data;
    }

    public void RestoreFromSaveData(HeavyObjectData data)
    {
        if (data == null) return;

        transform.position = data.position;
        transform.rotation = data.rotation;
        transform.localScale = data.scale;

        if (data.isActive && !gameObject.activeSelf)
        {
            gameObject.SetActive(true);
        }

        hasBeenMoved = data.hasBeenMoved;
        isCurrentlyMoving = data.isCurrentlyMoving;

        if (rb != null && (data.isCurrentlyMoving || data.hasBeenMoved))
        {
            rb.linearVelocity = data.velocity;
            rb.angularVelocity = data.angularVelocity;
        }

        lastKnownPosition = transform.position;
        lastKnownRotation = transform.rotation;

        if (isCurrentlyMoving)
        {
            SetMovingState(true);
        }
    }

    public void ForceSave()
    {
        NotifyPersistenceManagerOfMovement();
    }

    [ContextMenu("Reset to Initial State")]
    public void ResetToInitialState()
    {
        transform.position = initialPosition;
        transform.rotation = initialRotation;
        hasBeenMoved = false;
        isCurrentlyMoving = false;

        if (rb != null)
        {
            rb.linearVelocity = Vector3.zero;
            rb.angularVelocity = Vector3.zero;
        }

        lastKnownPosition = initialPosition;
        lastKnownRotation = initialRotation;

        SetMovingState(false);
        NotifyPersistenceManagerOfMovement();
    }

    public static List<HeavyObjectManager> GetActiveMovingObjects()
    {
        return new List<HeavyObjectManager>(activeMovingObjects);
    }

    public static List<HeavyObjectManager> GetAllHeavyObjects()
    {
        return new List<HeavyObjectManager>(allHeavyObjects.Values);
    }

    public static HeavyObjectManager FindByUniqueId(string id)
    {
        allHeavyObjects.TryGetValue(id, out HeavyObjectManager obj);
        return obj;
    }
}

[System.Serializable]
public class HeavyObjectData
{
    public string id;
    public string objectName;
    public string category;
    public Vector3 position;
    public Quaternion rotation;
    public Vector3 scale = Vector3.one;
    public bool hasBeenMoved;
    public bool isCurrentlyMoving;
    public bool isActive = true;
    public Vector3 velocity = Vector3.zero;
    public Vector3 angularVelocity = Vector3.zero;
}