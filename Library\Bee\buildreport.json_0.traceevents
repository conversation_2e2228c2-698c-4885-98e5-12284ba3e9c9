{ "pid": 70896, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 70896, "tid": 1, "ts": 1755813989265852, "dur": 6505, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 70896, "tid": 1, "ts": 1755813989272361, "dur": 126000, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 70896, "tid": 1, "ts": 1755813989398374, "dur": 3410, "ph": "X", "name": "Write<PERSON>son", "args": {} },
{ "pid": 70896, "tid": 2308, "ts": 1755813990975470, "dur": 1697, "ph": "X", "name": "", "args": {} },
{ "pid": 70896, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 70896, "tid": 12884901888, "ts": 1755813989263457, "dur": 13328, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 70896, "tid": 12884901888, "ts": 1755813989276788, "dur": 1684838, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 70896, "tid": 12884901888, "ts": 1755813989277946, "dur": 2524, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 70896, "tid": 12884901888, "ts": 1755813989280476, "dur": 1472, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 70896, "tid": 12884901888, "ts": 1755813989281952, "dur": 46, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 70896, "tid": 12884901888, "ts": 1755813989282000, "dur": 766, "ph": "X", "name": "ProcessMessages 483", "args": {} },
{ "pid": 70896, "tid": 12884901888, "ts": 1755813989282770, "dur": 5692, "ph": "X", "name": "ReadAsync 483", "args": {} },
{ "pid": 70896, "tid": 12884901888, "ts": 1755813989288466, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 70896, "tid": 12884901888, "ts": 1755813989288469, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 70896, "tid": 12884901888, "ts": 1755813989288499, "dur": 572, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 70896, "tid": 12884901888, "ts": 1755813989289074, "dur": 1650034, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 70896, "tid": 12884901888, "ts": 1755813990939119, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 70896, "tid": 12884901888, "ts": 1755813990939124, "dur": 541, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 70896, "tid": 12884901888, "ts": 1755813990939669, "dur": 1465, "ph": "X", "name": "ProcessMessages 15973", "args": {} },
{ "pid": 70896, "tid": 12884901888, "ts": 1755813990941136, "dur": 5595, "ph": "X", "name": "ReadAsync 15973", "args": {} },
{ "pid": 70896, "tid": 12884901888, "ts": 1755813990946740, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 70896, "tid": 12884901888, "ts": 1755813990946744, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 70896, "tid": 12884901888, "ts": 1755813990946789, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 70896, "tid": 12884901888, "ts": 1755813990946791, "dur": 231, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 70896, "tid": 12884901888, "ts": 1755813990947028, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 70896, "tid": 12884901888, "ts": 1755813990947060, "dur": 355, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 70896, "tid": 12884901888, "ts": 1755813990947416, "dur": 13390, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 70896, "tid": 2308, "ts": 1755813990977171, "dur": 38, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 70896, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 70896, "tid": 8589934592, "ts": 1755813989260222, "dur": 141618, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 70896, "tid": 8589934592, "ts": 1755813989401842, "dur": 3, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 70896, "tid": 8589934592, "ts": 1755813989401847, "dur": 1648, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 70896, "tid": 2308, "ts": 1755813990977211, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 70896, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 70896, "tid": 4294967296, "ts": 1755813989241477, "dur": 1721260, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 70896, "tid": 4294967296, "ts": 1755813989244846, "dur": 10150, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 70896, "tid": 4294967296, "ts": 1755813990962797, "dur": 4218, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 70896, "tid": 4294967296, "ts": 1755813990965561, "dur": 32, "ph": "X", "name": "await ScriptUpdaters", "args": {} },
{ "pid": 70896, "tid": 4294967296, "ts": 1755813990967358, "dur": 20, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 70896, "tid": 2308, "ts": 1755813990977219, "dur": 8, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1755813989275642, "dur":52, "ph":"X", "name": "IPC_Client_InitializeAndConnectToParent",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755813989275729, "dur":4172, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755813989279912, "dur":170, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755813989280116, "dur":1389, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755813989281541, "dur":74, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755813989281616, "dur":1666443, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755813990948060, "dur":171, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755813990948349, "dur":5087, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1755813989281092, "dur":530, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755813989282020, "dur":1177, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":1, "ts":1755813989281633, "dur":7151, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":1, "ts":1755813989290631, "dur":1651015, "ph":"X", "name": "BuildPlayerDataGenerator",  "args": { "detail":"Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":2, "ts":1755813989281315, "dur":321, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755813989281637, "dur":122781, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755813989414953, "dur":563, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.2.0f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":2, "ts":1755813989404419, "dur":11103, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755813989415522, "dur":1532552, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755813989281295, "dur":334, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755813989281652, "dur":1666404, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755813989281351, "dur":298, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755813989281650, "dur":1666410, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755813989281364, "dur":280, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755813989281644, "dur":133881, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755813989415526, "dur":1532535, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755813989281454, "dur":206, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755813989281660, "dur":1666404, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755813989281500, "dur":172, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755813989281673, "dur":1666397, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755813989281396, "dur":259, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755813989281656, "dur":1666402, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755813989281482, "dur":193, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755813989281675, "dur":1666394, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755813989281534, "dur":132, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755813989281667, "dur":1666409, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755813989281561, "dur":107, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755813989281668, "dur":1666415, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755813989281588, "dur":77, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755813989281665, "dur":1666401, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755813990960335, "dur":1199, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 70896, "tid": 2308, "ts": 1755813990977781, "dur": 2222, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 70896, "tid": 2308, "ts": 1755813990980183, "dur": 2642, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 70896, "tid": 2308, "ts": 1755813990974257, "dur": 9438, "ph": "X", "name": "Write chrome-trace events", "args": {} },
