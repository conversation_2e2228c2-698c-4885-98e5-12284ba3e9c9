%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d7fd9488000d3734a9e00ee676215985, type: 3}
  m_Name: StartWorld Volume
  m_EditorClassIdentifier: 
  components:
  - {fileID: 2654565735820578127}
  - {fileID: 5180576656532193010}
  - {fileID: 268642086590810294}
  - {fileID: 1029957712089816240}
  - {fileID: 3322325073836943779}
--- !u!114 &268642086590810294
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2d08ce26990eb1a4a9177b860541e702, type: 3}
  m_Name: Exposure
  m_EditorClassIdentifier: 
  active: 0
  mode:
    m_OverrideState: 1
    m_Value: 0
  meteringMode:
    m_OverrideState: 1
    m_Value: 2
  luminanceSource:
    m_OverrideState: 0
    m_Value: 1
  fixedExposure:
    m_OverrideState: 1
    m_Value: 5.21267
  compensation:
    m_OverrideState: 0
    m_Value: 0
  limitMin:
    m_OverrideState: 1
    m_Value: 0.5
  limitMax:
    m_OverrideState: 0
    m_Value: 14
  curveMap:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: -10
        value: -10
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 20
        value: 20
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  limitMinCurveMap:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: -10
        value: -12
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 20
        value: 18
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  limitMaxCurveMap:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: -10
        value: -8
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 20
        value: 22
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  adaptationMode:
    m_OverrideState: 0
    m_Value: 1
  adaptationSpeedDarkToLight:
    m_OverrideState: 0
    m_Value: 3
  adaptationSpeedLightToDark:
    m_OverrideState: 0
    m_Value: 1
  weightTextureMask:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  histogramPercentages:
    m_OverrideState: 0
    m_Value: {x: 40, y: 90}
  histogramUseCurveRemapping:
    m_OverrideState: 0
    m_Value: 0
  targetMidGray:
    m_OverrideState: 0
    m_Value: 0
  centerAroundExposureTarget:
    m_OverrideState: 0
    m_Value: 0
  proceduralCenter:
    m_OverrideState: 0
    m_Value: {x: 0.5, y: 0.5}
  proceduralRadii:
    m_OverrideState: 0
    m_Value: {x: 0.3, y: 0.3}
  maskMinIntensity:
    m_OverrideState: 0
    m_Value: -30
  maskMaxIntensity:
    m_OverrideState: 0
    m_Value: 30
  proceduralSoftness:
    m_OverrideState: 0
    m_Value: 0.5
--- !u!114 &1029957712089816240
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a81bcacc415a1f743bfdf703afc52027, type: 3}
  m_Name: GradientSky
  m_EditorClassIdentifier: 
  active: 0
  rotation:
    m_OverrideState: 1
    m_Value: 0
  skyIntensityMode:
    m_OverrideState: 1
    m_Value: 2
  exposure:
    m_OverrideState: 1
    m_Value: 0
  multiplier:
    m_OverrideState: 1
    m_Value: 0.33
  upperHemisphereLuxValue:
    m_OverrideState: 1
    m_Value: 1
  upperHemisphereLuxColor:
    m_OverrideState: 1
    m_Value: {x: 0, y: 0, z: 0}
  desiredLuxValue:
    m_OverrideState: 1
    m_Value: 20000
  updateMode:
    m_OverrideState: 1
    m_Value: 0
  updatePeriod:
    m_OverrideState: 1
    m_Value: 0
  includeSunInBaking:
    m_OverrideState: 1
    m_Value: 0
  top:
    m_OverrideState: 1
    m_Value: {r: 0.16981131, g: 0.16981131, b: 0.16981131, a: 1}
  middle:
    m_OverrideState: 1
    m_Value: {r: 0.039546248, g: 0.039546248, b: 0.039546248, a: 1}
  bottom:
    m_OverrideState: 1
    m_Value: {r: 0.039546248, g: 0.039546248, b: 0.039546248, a: 1}
  gradientDiffusion:
    m_OverrideState: 1
    m_Value: 5.65
--- !u!114 &2654565735820578127
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 953beb541740ddc499d005ee80c9ff29, type: 3}
  m_Name: Fog
  m_EditorClassIdentifier: 
  active: 1
  quality:
    m_OverrideState: 0
    m_Value: 3
  enabled:
    m_OverrideState: 1
    m_Value: 1
  colorMode:
    m_OverrideState: 1
    m_Value: 0
  color:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  tint:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  maxFogDistance:
    m_OverrideState: 0
    m_Value: 5000
  mipFogMaxMip:
    m_OverrideState: 0
    m_Value: 0.5
  mipFogNear:
    m_OverrideState: 0
    m_Value: 0
  mipFogFar:
    m_OverrideState: 0
    m_Value: 1000
  baseHeight:
    m_OverrideState: 1
    m_Value: -7777
  maximumHeight:
    m_OverrideState: 1
    m_Value: 1111
  meanFreePath:
    m_OverrideState: 1
    m_Value: 600
  enableVolumetricFog:
    m_OverrideState: 1
    m_Value: 1
  albedo:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  globalLightProbeDimmer:
    m_OverrideState: 0
    m_Value: 1
  depthExtent:
    m_OverrideState: 0
    m_Value: 6400
  denoisingMode:
    m_OverrideState: 0
    m_Value: 2
  anisotropy:
    m_OverrideState: 0
    m_Value: 0
  sliceDistributionUniformity:
    m_OverrideState: 0
    m_Value: 0.75
  multipleScatteringIntensity:
    m_OverrideState: 0
    m_Value: 0
  m_FogControlMode:
    m_OverrideState: 0
    m_Value: 0
  screenResolutionPercentage:
    m_OverrideState: 0
    m_Value: 12.5
  volumeSliceCount:
    m_OverrideState: 0
    m_Value: 64
  m_VolumetricFogBudget:
    m_OverrideState: 0
    m_Value: 0.5
  m_ResolutionDepthRatio:
    m_OverrideState: 0
    m_Value: 0.5
  directionalLightsOnly:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &3322325073836943779
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: da5ab44aadfb1804db5fd470983ac1b8, type: 3}
  m_Name: LiftGammaGain
  m_EditorClassIdentifier: 
  active: 0
  lift:
    m_OverrideState: 0
    m_Value: {x: 1, y: 1, z: 1, w: 0}
  gamma:
    m_OverrideState: 0
    m_Value: {x: 1, y: 1, z: 1, w: 0}
  gain:
    m_OverrideState: 0
    m_Value: {x: 1, y: 1, z: 1, w: 0}
--- !u!114 &5180576656532193010
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 24f077503be6ae942a1e1245dbd53ea9, type: 3}
  m_Name: Bloom
  m_EditorClassIdentifier: 
  active: 1
  quality:
    m_OverrideState: 1
    m_Value: 1
  threshold:
    m_OverrideState: 1
    m_Value: 0
  intensity:
    m_OverrideState: 1
    m_Value: 0.22
  scatter:
    m_OverrideState: 1
    m_Value: 0.7
  tint:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  dirtTexture:
    m_OverrideState: 1
    m_Value: {fileID: 0}
  dirtIntensity:
    m_OverrideState: 1
    m_Value: 0
  anamorphic:
    m_OverrideState: 1
    m_Value: 1
  m_Resolution:
    m_OverrideState: 1
    m_Value: 2
  m_HighQualityPrefiltering:
    m_OverrideState: 1
    m_Value: 0
  m_HighQualityFiltering:
    m_OverrideState: 1
    m_Value: 1
