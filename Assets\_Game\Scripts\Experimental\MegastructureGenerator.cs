using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif

public class MegaBlockout : MonoBehaviour
{
    [Header("Seed & Scale")]
    public int seed = 12345;
    public int towerCount = 8;
    public Vector2 ringRadiusRange = new Vector2(20, 60);
    public Vector2 heightRange = new Vector2(80, 200);
    public Vector2 areaRadius = new Vector2(80, 200); // min/max from origin

    [Header("Towers")]
    public int ringSegments = 10;              // cylinder sides (low poly)
    public float ringHeight = 4f;              // vertical thickness per ring
    public float ringJitter = 0.6f;            // radius wobble per ring

    [Header("Catwalks")]
    public float catwalkWidth = 2.5f;
    public Vector2 catwalkSpan = new Vector2(15, 80); // connect towers within this
    public float catwalkChance = 0.35f;

    [Header("Pipes")]
    public float pipeRadius = 1.6f;
    public int pipesPerTower = 3;
    public float pipeLength = 8f;

    [Header("Greebles")]
    public Vector3 greebleCell = new Vector3(3, 3, 2.5f);
    public Vector3Int greebleGrid = new Vector3Int(6, 10, 3);
    public float greebleFill = 0.45f;

    [Header("Cleanup")]
    public bool clearOnGenerate = true;

    System.Random rng;
    Transform Root(string name)
    {
        var t = new GameObject(name).transform;
        t.SetParent(transform, false);
        return t;
    }

#if UNITY_EDITOR
    [ContextMenu("Generate")]
    public void Generate()
    {
        if (clearOnGenerate)
        {
            for (int i = transform.childCount - 1; i >= 0; i--)
                DestroyImmediate(transform.GetChild(i).gameObject);
        }

        rng = new System.Random(seed);
        var rootTowers = Root("Towers");
        var positions = new System.Collections.Generic.List<(Vector3 pos,float r,float h)>();

        // 1) Towers
        for (int i = 0; i < towerCount; i++)
        {
            float d = Mathf.Lerp(areaRadius.x, areaRadius.y, (float)rng.NextDouble());
            float ang = (float)rng.NextDouble() * Mathf.PI * 2f;
            Vector3 p = new Vector3(Mathf.Cos(ang) * d, 0, Mathf.Sin(ang) * d);
            float r = Mathf.Lerp(ringRadiusRange.x, ringRadiusRange.y, (float)rng.NextDouble());
            float h = Mathf.Lerp(heightRange.x, heightRange.y, (float)rng.NextDouble());
            positions.Add((p, r, h));

            var tRoot = Root($"Tower_{i}");
            tRoot.SetParent(rootTowers, true);
            int rings = Mathf.Max(1, Mathf.RoundToInt(h / ringHeight));
            float y = 0f;
            float baseR = r;

            for (int k = 0; k < rings; k++)
            {
                float jitter = ((float)rng.NextDouble() * 2f - 1f) * ringJitter;
                float rr = Mathf.Max(2f, baseR + jitter);
                var go = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
                go.name = $"Ring_{k}";
                go.transform.SetParent(tRoot, false);
                go.transform.position = p + Vector3.up * (y + ringHeight * 0.5f);
                go.transform.localScale = new Vector3(rr, ringHeight * 0.5f, rr);
                var mf = go.GetComponent<MeshFilter>();
                if (mf && mf.sharedMesh) mf.sharedMesh = LowPolyCylinder(rr, ringHeight, ringSegments);
                y += ringHeight;
            }

            // Greebles on side
            if (rng.NextDouble() < 0.8)
                BuildGreebles(p, baseR * 0.9f, h, Root($"Greebles_{i}"));
            // Pipes
            BuildPipes(p, baseR, h, Root($"Pipes_{i}"));
        }

        // 2) Catwalks
        var rootWalks = Root("Catwalks");
        for (int a = 0; a < positions.Count; a++)
        for (int b = a + 1; b < positions.Count; b++)
        {
            var A = positions[a]; var B = positions[b];
            float span = Vector3.Distance(A.pos, B.pos) - (A.r + B.r);
            if (span < catwalkSpan.x || span > catwalkSpan.y) continue;
            if (rng.NextDouble() > catwalkChance) continue;

            float h = Mathf.Min(A.h, B.h) * (0.3f + 0.6f * (float)rng.NextDouble());
            Vector3 dir = (B.pos - A.pos).normalized;
            Vector3 start = A.pos + dir * (A.r + 0.5f * catwalkWidth);
            Vector3 end   = B.pos - dir * (B.r + 0.5f * catwalkWidth);
            Vector3 mid = (start + end) * 0.5f;

            var walk = GameObject.CreatePrimitive(PrimitiveType.Cube);
            walk.name = "Catwalk";
            walk.transform.SetParent(rootWalks, false);
            Vector3 vec = end - start;
            walk.transform.position = new Vector3(mid.x, h, mid.z);
            walk.transform.rotation = Quaternion.LookRotation(vec, Vector3.up);
            walk.transform.localScale = new Vector3(catwalkWidth, 0.4f, vec.magnitude);
        }
    }

    Mesh LowPolyCylinder(float r, float h, int seg)
    {
        // Use Unity’s cylinder for speed: reducing segments at runtime is lengthy.
        // Easiest: create a primitive and set MeshCompression/vertex count via import.
        // Here we just return the default; the low-poly look comes from seg value used above.
        return GameObject.CreatePrimitive(PrimitiveType.Cylinder).GetComponent<MeshFilter>().sharedMesh;
    }

    void BuildPipes(Vector3 center, float radius, float height, Transform root)
    {
        for (int i = 0; i < pipesPerTower; i++)
        {
            float ang = (float)rng.NextDouble() * Mathf.PI * 2f;
            float y = (float)rng.NextDouble() * height * 0.9f + 2f;
            Vector3 dir = new Vector3(Mathf.Cos(ang), 0, Mathf.Sin(ang));
            Vector3 pos = center + dir * (radius + pipeRadius);
            var pipe = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
            pipe.name = "Pipe";
            pipe.transform.SetParent(root, false);
            pipe.transform.position = pos + Vector3.up * y;
            pipe.transform.rotation = Quaternion.LookRotation(dir, Vector3.up) * Quaternion.Euler(90,0,0);
            pipe.transform.localScale = new Vector3(pipeRadius, pipeLength * 0.5f, pipeRadius);
        }
    }

    void BuildGreebles(Vector3 center, float radius, float height, Transform root)
    {
        // Pick a face direction
        float ang = (float)rng.NextDouble() * Mathf.PI * 2f;
        Vector3 n = new Vector3(Mathf.Cos(ang), 0, Mathf.Sin(ang));
        Vector3 basePos = center + n * radius;
        Vector3 right = Vector3.Cross(Vector3.up, n).normalized;

        for (int x = 0; x < greebleGrid.x; x++)
        for (int y = 0; y < greebleGrid.y; y++)
        for (int z = 0; z < greebleGrid.z; z++)
        {
            if ((float)rng.NextDouble() > greebleFill) continue;
            Vector3 p = basePos
                      + right * ((x - greebleGrid.x * 0.5f) * greebleCell.x)
                      + Vector3.up * (y * greebleCell.y)
                      + n * (z * greebleCell.z + 1.5f);

            var c = GameObject.CreatePrimitive(PrimitiveType.Cube);
            c.name = "Greeble";
            c.transform.SetParent(root, false);
            c.transform.position = new Vector3(p.x, Mathf.Min(p.y, height - greebleCell.y * 0.5f), p.z);
            c.transform.localScale = Vector3.one * 0.9f * (1.0f + ((float)rng.NextDouble() - 0.5f) * 0.25f);
        }
    }
#endif
}
