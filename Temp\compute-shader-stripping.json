{"totalVariantsIn": 16288, "totalVariantsOut": 2246, "shaders": [{"inputVariants": 4, "outputVariants": 0, "name": "StageSetupSegment", "pipelines": [{"inputVariants": 4, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 2.6852}, {"inputVariants": 2, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.007200000000000001}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "DepthOfFieldCoC", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: KMainPhysical", "stripTimeMs": 0.0461}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0137}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: KMainPhysical", "stripTimeMs": 0.0134}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0129}]}]}, {"inputVariants": 720, "outputVariants": 80, "name": "WaterLighting", "pipelines": [{"inputVariants": 720, "outputVariants": 80, "pipeline": "", "variants": [{"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterClearIndirect", "stripTimeMs": 0.1441}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterClassifyTiles", "stripTimeMs": 0.11660000000000001}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: PrepareSSRIndirect", "stripTimeMs": 0.11030000000000001}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterDeferredLighting_Variant0", "stripTimeMs": 0.1107}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterDeferredLighting_Variant1", "stripTimeMs": 0.0721}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterDeferredLighting_Variant2", "stripTimeMs": 0.0713}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterDeferredLighting_Variant3", "stripTimeMs": 0.06960000000000001}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterDeferredLighting_Variant4", "stripTimeMs": 0.0699}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterFogIndirect", "stripTimeMs": 0.0697}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterFogTransmittanceIndirect", "stripTimeMs": 0.0678}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterClearIndirect", "stripTimeMs": 0.0743}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterClassifyTiles", "stripTimeMs": 0.0902}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: PrepareSSRIndirect", "stripTimeMs": 0.07}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterDeferredLighting_Variant0", "stripTimeMs": 0.0692}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterDeferredLighting_Variant1", "stripTimeMs": 0.07060000000000001}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterDeferredLighting_Variant2", "stripTimeMs": 0.07060000000000001}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterDeferredLighting_Variant3", "stripTimeMs": 0.06960000000000001}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterDeferredLighting_Variant4", "stripTimeMs": 0.0684}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterFogIndirect", "stripTimeMs": 0.0752}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterFogTransmittanceIndirect", "stripTimeMs": 0.069}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "LensFlareMergeOcclusionDataDriven", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainCS", "stripTimeMs": 0.0212}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainCS", "stripTimeMs": 0.0094}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "OccluderDepthPyramidKernels", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>D<PERSON><PERSON><PERSON>", "stripTimeMs": 0.0403}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>D<PERSON><PERSON><PERSON>", "stripTimeMs": 0.0327}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "ProbeVolumeUploadData", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: UploadData", "stripTimeMs": 0.0517}, {"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: UploadData", "stripTimeMs": 0.0341}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "BakeCloudTexture", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: BakeCloudTexture", "stripTimeMs": 0.0196}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: BakeCloudTexture", "stripTimeMs": 0.0051}]}]}, {"inputVariants": 13392, "outputVariants": 372, "name": "Deferred", "pipelines": [{"inputVariants": 13392, "outputVariants": 372, "pipeline": "", "variants": [{"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Direct_Fptl", "stripTimeMs": 0.6908000000000001}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Direct_Fptl_DebugDisplay", "stripTimeMs": 0.6096}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant0", "stripTimeMs": 0.7011000000000001}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant1", "stripTimeMs": 0.7304}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant2", "stripTimeMs": 0.68}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant3", "stripTimeMs": 0.7304}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant4", "stripTimeMs": 0.668}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant5", "stripTimeMs": 0.6516000000000001}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant6", "stripTimeMs": 0.4178}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant7", "stripTimeMs": 0.42910000000000004}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant8", "stripTimeMs": 0.4429}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant9", "stripTimeMs": 0.6421}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant10", "stripTimeMs": 0.4743}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant11", "stripTimeMs": 0.6776}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant12", "stripTimeMs": 0.6816}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant13", "stripTimeMs": 0.4917}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant14", "stripTimeMs": 0.41900000000000004}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant15", "stripTimeMs": 0.42850000000000005}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant16", "stripTimeMs": 0.4209}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant17", "stripTimeMs": 0.5446}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant18", "stripTimeMs": 0.6568}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant19", "stripTimeMs": 0.7000000000000001}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant20", "stripTimeMs": 0.5254}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant21", "stripTimeMs": 0.41900000000000004}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant22", "stripTimeMs": 0.44780000000000003}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant23", "stripTimeMs": 0.4303}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant24", "stripTimeMs": 0.5668000000000001}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant25", "stripTimeMs": 0.4867}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant26", "stripTimeMs": 0.5088}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant27", "stripTimeMs": 0.43510000000000004}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant28", "stripTimeMs": 0.4292}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Direct_Fptl", "stripTimeMs": 0.4612}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Direct_Fptl_DebugDisplay", "stripTimeMs": 0.7149}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant0", "stripTimeMs": 0.7324}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant1", "stripTimeMs": 0.7237}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant2", "stripTimeMs": 0.7091000000000001}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant3", "stripTimeMs": 0.4693}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant4", "stripTimeMs": 0.4253}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant5", "stripTimeMs": 0.46130000000000004}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant6", "stripTimeMs": 0.42400000000000004}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant7", "stripTimeMs": 0.42360000000000003}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant8", "stripTimeMs": 0.4379}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant9", "stripTimeMs": 0.4268}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant10", "stripTimeMs": 0.4425}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant11", "stripTimeMs": 0.43560000000000004}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant12", "stripTimeMs": 0.45120000000000005}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant13", "stripTimeMs": 0.42050000000000004}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant14", "stripTimeMs": 0.4193}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant15", "stripTimeMs": 0.5572}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant16", "stripTimeMs": 0.42850000000000005}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant17", "stripTimeMs": 0.42900000000000005}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant18", "stripTimeMs": 0.4284}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant19", "stripTimeMs": 0.42260000000000003}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant20", "stripTimeMs": 0.6972}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant21", "stripTimeMs": 0.6773}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant22", "stripTimeMs": 0.6748000000000001}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant23", "stripTimeMs": 0.6731}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant24", "stripTimeMs": 0.6691}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant25", "stripTimeMs": 0.6741}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant26", "stripTimeMs": 0.7334}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant27", "stripTimeMs": 0.4224}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant28", "stripTimeMs": 0.7364}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "lightlistbuild-clustered", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_NoDepthRT", "stripTimeMs": 0.019}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_MSAA", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_NoDepthRT_SrcBigTile", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_SrcBigTile", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_MSAA_SrcBigTile", "stripTimeMs": 0.0061}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_Oblique", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_MSAA_Oblique", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_SrcBigTile_Oblique", "stripTimeMs": 0.0057}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_MSAA_SrcBigTile_Oblique", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_NoDepthRT", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_MSAA", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_NoDepthRT_SrcBigTile", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_SrcBigTile", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_MSAA_SrcBigTile", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_Oblique", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_MSAA_Oblique", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_SrcBigTile_Oblique", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_MSAA_SrcBigTile_Oblique", "stripTimeMs": 0.0031000000000000003}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "WaterFoam", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReprojectFoam", "stripTimeMs": 0.0245}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AttenuateFoam", "stripTimeMs": 0.006200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReprojectFoam", "stripTimeMs": 0.0053}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AttenuateFoam", "stripTimeMs": 0.005200000000000001}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "PaniniProjection", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0427}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.009300000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "BloomBlur", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.016900000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: K<PERSON><PERSON><PERSON>mple", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: K<PERSON><PERSON><PERSON>mple", "stripTimeMs": 0.0027}]}]}, {"inputVariants": 8, "outputVariants": 4, "name": "FXAA", "pipelines": [{"inputVariants": 8, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 2, "variantName": "Kernel: FXAA", "stripTimeMs": 0.0347}, {"inputVariants": 4, "outputVariants": 2, "variantName": "Kernel: FXAA", "stripTimeMs": 0.0261}]}]}, {"inputVariants": 4, "outputVariants": 0, "name": "StageRasterBin", "pipelines": [{"inputVariants": 4, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0159}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: MainArgs", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.005200000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: MainArgs", "stripTimeMs": 0.0025}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "ApplyExposure", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0165}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0082}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "DepthOfFieldCoCDilate", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.02}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0088}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "lightlistbuild-clearatomic", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearAtomic", "stripTimeMs": 0.0177}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearAtomic", "stripTimeMs": 0.0032}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "<PERSON><PERSON><PERSON><PERSON>", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.036500000000000005}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.022000000000000002}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "WaterLine", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearWaterLine", "stripTimeMs": 0.0178}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LineEvaluation1D", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BoundsPropagation", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearWaterLine", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LineEvaluation1D", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BoundsPropagation", "stripTimeMs": 0.0037}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "HistogramExposure", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KHistogramGen", "stripTimeMs": 0.0216}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KHistogramReduce", "stripTimeMs": 0.0082}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KHistogramGen", "stripTimeMs": 0.0066}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KHistogramReduce", "stripTimeMs": 0.0068000000000000005}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "ProbeVolumeSamplingDebugPositionNormal", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ComputePositionNormal", "stripTimeMs": 0.009600000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ComputePositionNormal", "stripTimeMs": 0.0013000000000000002}]}]}, {"inputVariants": 18, "outputVariants": 18, "name": "VFXCopyBuffer", "pipelines": [{"inputVariants": 18, "outputVariants": 18, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXCopyBuffer", "stripTimeMs": 0.0099}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXCopyStructBuffer", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXInitDeadListBuffer", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXZeroInitBuffer", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXZeroInitBufferUint", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchCopyCountUint", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchCopyCountKvp", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXInitBoundsBuffer", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXCopyStructBufferSelf", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXCopyBuffer", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXCopyStructBuffer", "stripTimeMs": 0.0051}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXInitDeadListBuffer", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXZeroInitBuffer", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXZeroInitBufferUint", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchCopyCountUint", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchCopyCountKvp", "stripTimeMs": 0.0047}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXInitBoundsBuffer", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXCopyStructBufferSelf", "stripTimeMs": 0.0023}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "BloomPrefilter", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0284}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0135}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "VolumetricMaterial", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeVolumetricMaterialRenderingParameters", "stripTimeMs": 0.015600000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeVolumetricMaterialRenderingParameters", "stripTimeMs": 0.0032}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "ScreenSpaceMultipleScattering", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScreenSpaceMultipleScattering", "stripTimeMs": 0.014}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScreenSpaceMultipleScattering", "stripTimeMs": 0.0046}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "InstanceTransformUpdateKernels", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ScatterInitTransformMain", "stripTimeMs": 0.024800000000000003}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ScatterUpdateTransformMain", "stripTimeMs": 0.007}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ScatterUpdateMotionMain", "stripTimeMs": 0.0068000000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ScatterUpdateProbesMain", "stripTimeMs": 0.007}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ScatterInitTransformMain", "stripTimeMs": 0.006500000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ScatterUpdateTransformMain", "stripTimeMs": 0.0083}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ScatterUpdateMotionMain", "stripTimeMs": 0.0059}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ScatterUpdateProbesMain", "stripTimeMs": 0.0068000000000000005}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "MotionBlur", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: MotionBlurCS", "stripTimeMs": 0.0284}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: MotionBlurCS", "stripTimeMs": 0.0108}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "GTAOSpatialDenoise", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: SpatialDenoise", "stripTimeMs": 0.0164}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: SpatialDenoise", "stripTimeMs": 0.0056}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "DepthOfFieldMipSafe", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0223}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0079}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "GTAOTemporalDenoise", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TemporalDenoise", "stripTimeMs": 0.0252}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TemporalDenoise", "stripTimeMs": 0.0145}]}]}, {"inputVariants": 14, "outputVariants": 14, "name": "WaterSimulation", "pipelines": [{"inputVariants": 14, "outputVariants": 14, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: InitializePhillipsSpectrum", "stripTimeMs": 0.0179}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateDispersion", "stripTimeMs": 0.0048000000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateNormals", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateNormalsJacobian", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: PrepareCausticsGeometry", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateInstanceData", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateInstanceDataInfinite", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: InitializePhillipsSpectrum", "stripTimeMs": 0.006200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateDispersion", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateNormals", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateNormalsJacobian", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: PrepareCausticsGeometry", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateInstanceData", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateInstanceDataInfinite", "stripTimeMs": 0.0034000000000000002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "DownsampleVTFeedback", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0188}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainMSAA", "stripTimeMs": 0.0048000000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.005200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainMSAA", "stripTimeMs": 0.0039000000000000003}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "MotionBlurGenTilePass", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TileGenPass", "stripTimeMs": 0.026000000000000002}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TileGenPass", "stripTimeMs": 0.009600000000000001}]}]}, {"inputVariants": 64, "outputVariants": 64, "name": "StpSetup", "pipelines": [{"inputVariants": 64, "outputVariants": 64, "pipeline": "", "variants": [{"inputVariants": 32, "outputVariants": 32, "variantName": "Kernel: StpSetup", "stripTimeMs": 0.08320000000000001}, {"inputVariants": 32, "outputVariants": 32, "variantName": "Kernel: StpSetup", "stripTimeMs": 0.06910000000000001}]}]}, {"inputVariants": 48, "outputVariants": 24, "name": "LutBuilder3D", "pipelines": [{"inputVariants": 48, "outputVariants": 24, "pipeline": "", "variants": [{"inputVariants": 24, "outputVariants": 12, "variantName": "Kernel: KBuild", "stripTimeMs": 0.1442}, {"inputVariants": 24, "outputVariants": 12, "variantName": "Kernel: KBuild", "stripTimeMs": 0.12430000000000001}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "StpTaa", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: StpTaa", "stripTimeMs": 0.0538}, {"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: StpTaa", "stripTimeMs": 0.0352}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "DepthOfFieldPreCombineFar", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KMainPreCombineFar", "stripTimeMs": 0.0264}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KMainPreCombineFar", "stripTimeMs": 0.0053}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "ContactShadows", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: DeferredContactShadow", "stripTimeMs": 0.0262}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: DeferredContactShadow", "stripTimeMs": 0.0112}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "BakeCloudShadows", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: BakeCloudShadows", "stripTimeMs": 0.07400000000000001}, {"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: BakeCloudShadows", "stripTimeMs": 0.059500000000000004}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "DepthOfFieldGather", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: K<PERSON>ain<PERSON>ear", "stripTimeMs": 0.0396}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0175}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: K<PERSON>ain<PERSON>ear", "stripTimeMs": 0.0179}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0171}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "DepthOfFieldCoCReproject", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.024800000000000003}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0088}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "HairMultipleScatteringPreIntegration", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeAttenuationForward", "stripTimeMs": 0.0264}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeAttenuationBackward", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeAzimuthalScattering", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeLongitudinalScattering", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeAttenuationForward", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeAttenuationBackward", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeAzimuthalScattering", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeLongitudinalScattering", "stripTimeMs": 0.0034000000000000002}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "BlitAndExpose", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0211}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KAccumMain", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0047}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KAccumMain", "stripTimeMs": 0.0035}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "MomentShadows", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeMomentShadows", "stripTimeMs": 0.020200000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MomentSummedAreaTableHorizontal", "stripTimeMs": 0.0083}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MomentSummedAreaTableVertical", "stripTimeMs": 0.005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeMomentShadows", "stripTimeMs": 0.0051}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MomentSummedAreaTableHorizontal", "stripTimeMs": 0.0045000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MomentSummedAreaTableVertical", "stripTimeMs": 0.0046}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "ColorPyramid", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.027100000000000003}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KColorDownsample", "stripTimeMs": 0.01}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.009000000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KColorDownsample", "stripTimeMs": 0.0088}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "builddispatchindirect", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BuildIndirect", "stripTimeMs": 0.0187}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BuildIndirect", "stripTimeMs": 0.008}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "RandomDownsample", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Downsample", "stripTimeMs": 0.0117}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Downsample", "stripTimeMs": 0.0061}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "DebugHistogramImage", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KHistogramGen", "stripTimeMs": 0.0227}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KHistogramGen", "stripTimeMs": 0.0054}]}]}, {"inputVariants": 12, "outputVariants": 0, "name": "StagePrepare", "pipelines": [{"inputVariants": 12, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearCounters", "stripTimeMs": 0.0264}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearBins", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearClusters", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ComputeClusterRanges", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearCountersPer<PERSON><PERSON><PERSON>", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: UpdateOffsets", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearCounters", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearBins", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearClusters", "stripTimeMs": 0.0053}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ComputeClusterRanges", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearCountersPer<PERSON><PERSON><PERSON>", "stripTimeMs": 0.0068000000000000005}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: UpdateOffsets", "stripTimeMs": 0.0023}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "InstanceDataBufferUploadKernels", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainUploadScatterInstances", "stripTimeMs": 0.0148}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainUploadScatterInstances", "stripTimeMs": 0.0057}]}]}, {"inputVariants": 8, "outputVariants": 4, "name": "ContrastAdaptiveSharpen", "pipelines": [{"inputVariants": 8, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0198}, {"inputVariants": 2, "outputVariants": 1, "variantName": "Kernel: KInitialize", "stripTimeMs": 0.014}, {"inputVariants": 2, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0122}, {"inputVariants": 2, "outputVariants": 1, "variantName": "Kernel: KInitialize", "stripTimeMs": 0.0122}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "BloomUpsample", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0244}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.013000000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "MotionBlurNeighborhoodTilePass", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TileNeighbourhood", "stripTimeMs": 0.0148}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TileNeighbourhood", "stripTimeMs": 0.009000000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "lightlistbuild-bigtile", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: BigTileLightListGen", "stripTimeMs": 0.019}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: BigTileLightListGen", "stripTimeMs": 0.0058000000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "ProbeVolumeUploadDataL2", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UploadDataL2", "stripTimeMs": 0.0204}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UploadDataL2", "stripTimeMs": 0.0058000000000000005}]}]}, {"inputVariants": 4, "outputVariants": 0, "name": "DebugWaveform", "pipelines": [{"inputVariants": 4, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: KWaveformGather", "stripTimeMs": 0.0114}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: KWaveformClear", "stripTimeMs": 0.0012000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: KWaveformGather", "stripTimeMs": 0.0009000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: KWaveformClear", "stripTimeMs": 0.0026000000000000003}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "DepthPyramid", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KDepthDownsample8DualUav", "stripTimeMs": 0.011600000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KDepthDownsample8DualUav", "stripTimeMs": 0.0054}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "lightlistbuild", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: TileLightListGen", "stripTimeMs": 0.033}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: TileLightListGen", "stripTimeMs": 0.018500000000000003}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "ResolveStencilBuffer", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: Main", "stripTimeMs": 0.06810000000000001}, {"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: Main", "stripTimeMs": 0.038}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "PostSharpenPass", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: SharpenCS", "stripTimeMs": 0.0358}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: SharpenCS", "stripTimeMs": 0.0167}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "GTAO", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: GTAOMain", "stripTimeMs": 0.033600000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: GTAOMain", "stripTimeMs": 0.0167}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "BilateralUpsample", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralUpSampleColorHalf", "stripTimeMs": 0.0206}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralUpSampleColor", "stripTimeMs": 0.005200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralUpSampleColorHalf", "stripTimeMs": 0.0049}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralUpSampleColor", "stripTimeMs": 0.0046}]}]}, {"inputVariants": 42, "outputVariants": 42, "name": "GenSdfRayMap", "pipelines": [{"inputVariants": 42, "outputVariants": 42, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: InBucketSum", "stripTimeMs": 0.0182}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlockSums", "stripTimeMs": 0.0054}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: FinalSum", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ToTextureNormalized", "stripTimeMs": 0.007}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CopyTextures", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: JFA", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DistanceTransform", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Copy<PERSON><PERSON><PERSON>", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GenerateRayMapLocal", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SignPass6Rays", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SignPassNeighbors", "stripTimeMs": 0.005200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ToBlockSumBuffer", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearTexturesAndBuffers", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GenerateTrianglesUV", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ConservativeRasterization", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ChooseDirectionTriangleOnly", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SurfaceClosing", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RayMapScanX", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RayMapScanY", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RayMapScanZ", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: InBucketSum", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlockSums", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: FinalSum", "stripTimeMs": 0.0047}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ToTextureNormalized", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CopyTextures", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: JFA", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DistanceTransform", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Copy<PERSON><PERSON><PERSON>", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GenerateRayMapLocal", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SignPass6Rays", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SignPassNeighbors", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ToBlockSumBuffer", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearTexturesAndBuffers", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GenerateTrianglesUV", "stripTimeMs": 0.0054}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ConservativeRasterization", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ChooseDirectionTriangleOnly", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SurfaceClosing", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RayMapScanX", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RayMapScanY", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RayMapScanZ", "stripTimeMs": 0.0035}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "DepthOfFieldClearIndirectArgs", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON>lear", "stripTimeMs": 0.0206}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON>lear", "stripTimeMs": 0.0064}]}]}, {"inputVariants": 4, "outputVariants": 0, "name": "DebugVectorscope", "pipelines": [{"inputVariants": 4, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: KVectorscopeGather", "stripTimeMs": 0.009300000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: KVectorscopeClear", "stripTimeMs": 0.0015}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: KVectorscopeGather", "stripTimeMs": 0.0011}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: KVectorscopeClear", "stripTimeMs": 0.0012000000000000001}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "AmbientProbeConvolution", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AmbientProbeConvolutionDiffuse", "stripTimeMs": 0.010400000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AmbientProbeConvolutionVolumetric", "stripTimeMs": 0.0048000000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AmbientProbeConvolutionDiffuseVolumetric", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AmbientProbeConvolutionClouds", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AmbientProbeConvolutionDiffuse", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AmbientProbeConvolutionVolumetric", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AmbientProbeConvolutionDiffuseVolumetric", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AmbientProbeConvolutionClouds", "stripTimeMs": 0.0022}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "InScatteredRadiancePrecomputation", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: main", "stripTimeMs": 0.022000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: main", "stripTimeMs": 0.0059}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "scrbound", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: main", "stripTimeMs": 0.0194}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: main", "stripTimeMs": 0.005}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "TemporalFilter", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ValidateHistory", "stripTimeMs": 0.0071}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TemporalAccumulationSingle", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TemporalAccumulationSingleArray", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TemporalAccumulationColor", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TemporalAccumulationColorArray", "stripTimeMs": 0.0055000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CopyHistory", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlendHistorySingleArray", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlendHistorySingleArrayNoValidity", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlendHistoryColorArray", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: OutputHistoryArray", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ValidateHistory", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TemporalAccumulationSingle", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TemporalAccumulationSingleArray", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TemporalAccumulationColor", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TemporalAccumulationColorArray", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CopyHistory", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlendHistorySingleArray", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlendHistorySingleArrayNoValidity", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlendHistoryColorArray", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: OutputHistoryArray", "stripTimeMs": 0.0022}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "ClearLightLists", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearList", "stripTimeMs": 0.019200000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearList", "stripTimeMs": 0.0032}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "DoFCircleOfConfusion", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KMainCoCPhysical", "stripTimeMs": 0.013000000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KMainCoCManual", "stripTimeMs": 0.0092}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KMainCoCPhysical", "stripTimeMs": 0.0089}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KMainCoCManual", "stripTimeMs": 0.0083}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "DoFMinMaxDilate", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0219}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "ComputeGgxIblSampleData", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeGgxIblSampleData", "stripTimeMs": 0.014400000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeGgxIblSampleData", "stripTimeMs": 0.0045000000000000005}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "EyeCausticLUTGen", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SampleC<PERSON>tic", "stripTimeMs": 0.013000000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CopyToLUT", "stripTimeMs": 0.005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SampleC<PERSON>tic", "stripTimeMs": 0.0172}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CopyToLUT", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.005200000000000001}]}]}, {"inputVariants": 14, "outputVariants": 14, "name": "GPUPrefixSum", "pipelines": [{"inputVariants": 14, "outputVariants": 14, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainCalculateLevelDispatchArgsFromConst", "stripTimeMs": 0.0207}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainCalculateLevelDispatchArgsFromBuffer", "stripTimeMs": 0.0051}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumOnGroup", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumOnGroupExclusive", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumNextInput", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumResolveParent", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumResolveParentExclusive", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainCalculateLevelDispatchArgsFromConst", "stripTimeMs": 0.0045000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainCalculateLevelDispatchArgsFromBuffer", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumOnGroup", "stripTimeMs": 0.021400000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumOnGroupExclusive", "stripTimeMs": 0.0046}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumNextInput", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumResolveParent", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumResolveParentExclusive", "stripTimeMs": 0.0043}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "VFXPrefixSum", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXPrepareSingleInstance", "stripTimeMs": 0.0165}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchSumCount", "stripTimeMs": 0.0055000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchSumCount_128", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBuildPrefixSum", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBuildPrefixSum_128", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXPrepareSingleInstance", "stripTimeMs": 0.0045000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchSumCount", "stripTimeMs": 0.0053}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchSumCount_128", "stripTimeMs": 0.005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBuildPrefixSum", "stripTimeMs": 0.0045000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBuildPrefixSum_128", "stripTimeMs": 0.0026000000000000003}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "DepthOfFieldKernel", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KParametricBlurKernel", "stripTimeMs": 0.015700000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KParametricFloodfillKernel", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KParametricBlurKernel", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KParametricFloodfillKernel", "stripTimeMs": 0.0023}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "SkyLUTGenerator", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MultiScatteringLUT", "stripTimeMs": 0.0166}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SkyViewLUT", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AtmosphericScatteringLUTCamera", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AtmosphericScatteringLUTWorld", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AtmosphericScatteringBlur", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MultiScatteringLUT", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SkyViewLUT", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AtmosphericScatteringLUTCamera", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AtmosphericScatteringLUTWorld", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AtmosphericScatteringBlur", "stripTimeMs": 0.0024000000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "GTAOCopyHistory", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GTAODenoise_CopyHistory", "stripTimeMs": 0.018600000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GTAODenoise_CopyHistory", "stripTimeMs": 0.0074}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "ClearDebugBuffer", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: clearMain", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: clearMain", "stripTimeMs": 0.002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Texture3DAtlas", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.008700000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GenerateMipMap", "stripTimeMs": 0.0068000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.005200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GenerateMipMap", "stripTimeMs": 0.0039000000000000003}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "NaNKiller", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.030600000000000002}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0109}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "VFXFillIndirectArgs", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXIndirectArgs", "stripTimeMs": 0.0154}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXIndirectArgs", "stripTimeMs": 0.005200000000000001}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "DepthOfFieldTileMax", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0196}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0092}]}]}, {"inputVariants": 12, "outputVariants": 0, "name": "StageRasterFine", "pipelines": [{"inputVariants": 12, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0164}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0068000000000000005}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0048000000000000004}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0026000000000000003}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "UpdateStrips", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UpdateParticleStrip", "stripTimeMs": 0.011000000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UpdateParticleStrip", "stripTimeMs": 0.005200000000000001}]}]}, {"inputVariants": 24, "outputVariants": 16, "name": "ScreenSpaceGlobalIllumination", "pipelines": [{"inputVariants": 24, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 3, "outputVariants": 2, "variantName": "Kernel: TraceGlobalIllumination", "stripTimeMs": 0.018000000000000002}, {"inputVariants": 3, "outputVariants": 2, "variantName": "Kernel: TraceGlobalIlluminationHalf", "stripTimeMs": 0.0092}, {"inputVariants": 3, "outputVariants": 2, "variantName": "Kernel: ReprojectGlobalIllumination", "stripTimeMs": 0.0068000000000000005}, {"inputVariants": 3, "outputVariants": 2, "variantName": "Kernel: ReprojectGlobalIlluminationHalf", "stripTimeMs": 0.0078000000000000005}, {"inputVariants": 3, "outputVariants": 2, "variantName": "Kernel: TraceGlobalIllumination", "stripTimeMs": 0.007}, {"inputVariants": 3, "outputVariants": 2, "variantName": "Kernel: TraceGlobalIlluminationHalf", "stripTimeMs": 0.0063}, {"inputVariants": 3, "outputVariants": 2, "variantName": "Kernel: ReprojectGlobalIllumination", "stripTimeMs": 0.0063}, {"inputVariants": 3, "outputVariants": 2, "variantName": "Kernel: ReprojectGlobalIlluminationHalf", "stripTimeMs": 0.007}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "Exposure", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KFixedExposure", "stripTimeMs": 0.0173}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KManualCameraExposure", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KPrePass", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KReduction", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KFixedExposure", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KManualCameraExposure", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KPrePass", "stripTimeMs": 0.0048000000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KReduction", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0036000000000000003}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "WaterDeformation", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: FilterDeformation", "stripTimeMs": 0.0182}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: EvaluateDeformationSurfaceGradient", "stripTimeMs": 0.005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: FilterDeformation", "stripTimeMs": 0.0049}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: EvaluateDeformationSurfaceGradient", "stripTimeMs": 0.0067}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "GTAOBlurAndUpsample", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlurUpsample", "stripTimeMs": 0.0162}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralUpsampling", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BoxUpsampling", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Blur", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Blur_FullRes", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlurUpsample", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralUpsampling", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BoxUpsampling", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Blur", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Blur_FullRes", "stripTimeMs": 0.0023}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "PlanarReflectionFiltering", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: FilterPlanarReflection", "stripTimeMs": 0.0195}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DownScale", "stripTimeMs": 0.0054}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DepthConversion", "stripTimeMs": 0.0045000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: FilterPlanarReflection", "stripTimeMs": 0.0088}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DownScale", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DepthConversion", "stripTimeMs": 0.004}]}]}, {"inputVariants": 8, "outputVariants": 4, "name": "ProbeVolumeBlendStates", "pipelines": [{"inputVariants": 8, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 2, "variantName": "Kernel: BlendScenarios", "stripTimeMs": 0.0468}, {"inputVariants": 4, "outputVariants": 2, "variantName": "Kernel: BlendScenarios", "stripTimeMs": 0.0234}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "GPUCopy", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KSampleCopy4_1_x_8", "stripTimeMs": 0.022600000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KSampleCopy4_1_x_1", "stripTimeMs": 0.0058000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KSampleCopy4_1_x_8", "stripTimeMs": 0.0053}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KSampleCopy4_1_x_1", "stripTimeMs": 0.0047}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "StpPreTaa", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: StpPreTaa", "stripTimeMs": 0.0655}, {"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: StpPreTaa", "stripTimeMs": 0.0546}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "EncodeBC6H", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KEncodeFastCubemapMip", "stripTimeMs": 0.024800000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KEncodeFastCubemapMip", "stripTimeMs": 0.005200000000000001}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "SubsurfaceScattering", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: SubsurfaceScattering", "stripTimeMs": 0.037200000000000004}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: PackDiffusionProfile", "stripTimeMs": 0.023200000000000002}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: SubsurfaceScattering", "stripTimeMs": 0.0212}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: PackDiffusionProfile", "stripTimeMs": 0.0212}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "OcclusionCullingDebug", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearOcclusionDebug", "stripTimeMs": 0.0162}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearOcclusionDebug", "stripTimeMs": 0.0032}]}]}, {"inputVariants": 384, "outputVariants": 256, "name": "VolumetricLighting", "pipelines": [{"inputVariants": 384, "outputVariants": 256, "pipeline": "", "variants": [{"inputVariants": 192, "outputVariants": 128, "variantName": "Kernel: VolumetricLighting", "stripTimeMs": 0.5207}, {"inputVariants": 192, "outputVariants": 128, "variantName": "Kernel: VolumetricLighting", "stripTimeMs": 0.524}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "DiffuseDenoiser", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GeneratePointDistribution", "stripTimeMs": 0.020300000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralFilterSingle", "stripTimeMs": 0.0049}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralFilterColor", "stripTimeMs": 0.0056}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>ingle", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GatherColor", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GeneratePointDistribution", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralFilterSingle", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralFilterColor", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>ingle", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GatherColor", "stripTimeMs": 0.0057}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "DoFComputeSlowTiles", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ComputeSlowTiles", "stripTimeMs": 0.0222}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ComputeSlowTiles", "stripTimeMs": 0.0086}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "BuildProbabilityTables", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeConditionalDensities", "stripTimeMs": 0.022600000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeMarginalRowDensities", "stripTimeMs": 0.0058000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeConditionalDensities", "stripTimeMs": 0.0053}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeMarginalRowDensities", "stripTimeMs": 0.0053}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "FourierTransform", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RowPassTi_256", "stripTimeMs": 0.0211}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ColPassTi_256", "stripTimeMs": 0.007200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RowPassTi_128", "stripTimeMs": 0.0048000000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ColPassTi_128", "stripTimeMs": 0.007500000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RowPassTi_64", "stripTimeMs": 0.015700000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ColPassTi_64", "stripTimeMs": 0.0048000000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RowPassTi_256", "stripTimeMs": 0.0083}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ColPassTi_256", "stripTimeMs": 0.0046}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RowPassTi_128", "stripTimeMs": 0.0047}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ColPassTi_128", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RowPassTi_64", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ColPassTi_64", "stripTimeMs": 0.004}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "VolumeVoxelization", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: VolumeVoxelization", "stripTimeMs": 0.0267}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: VolumeVoxelization", "stripTimeMs": 0.0123}]}]}, {"inputVariants": 24, "outputVariants": 24, "name": "WaterEvaluation", "pipelines": [{"inputVariants": 24, "outputVariants": 24, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: FindVerticalDisplacements", "stripTimeMs": 0.0629}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: FindVerticalDisplacements", "stripTimeMs": 0.045200000000000004}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "AlphaCopy", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0233}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.005200000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "DoFCoCMinMax", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainCoCMinMax", "stripTimeMs": 0.0083}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainCoCMinMax", "stripTimeMs": 0.005200000000000001}]}]}, {"inputVariants": 48, "outputVariants": 48, "name": "DepthOfFieldCombine", "pipelines": [{"inputVariants": 48, "outputVariants": 48, "pipeline": "", "variants": [{"inputVariants": 24, "outputVariants": 24, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.07200000000000001}, {"inputVariants": 24, "outputVariants": 24, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0506}]}]}, {"inputVariants": 48, "outputVariants": 48, "name": "VrsTexture", "pipelines": [{"inputVariants": 48, "outputVariants": 48, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: TextureCopy", "stripTimeMs": 0.058}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: TextureReduce", "stripTimeMs": 0.0267}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: TextureCopy", "stripTimeMs": 0.0252}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: TextureReduce", "stripTimeMs": 0.025400000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "MotionBlurMergeTilePass", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileMerge", "stripTimeMs": 0.0178}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileMerge", "stripTimeMs": 0.0034000000000000002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "ClearUIntTextureArray", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearUIntTexture", "stripTimeMs": 0.0081}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearUIntTextureArray", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearUIntTexture", "stripTimeMs": 0.005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearUIntTextureArray", "stripTimeMs": 0.003}]}]}, {"inputVariants": 160, "outputVariants": 160, "name": "ScreenSpaceReflections", "pipelines": [{"inputVariants": 160, "outputVariants": 160, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsTracing", "stripTimeMs": 0.0228}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsReprojection", "stripTimeMs": 0.0165}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionBoth", "stripTimeMs": 0.014400000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionSourceOnly", "stripTimeMs": 0.0166}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionTargetOnly", "stripTimeMs": 0.014}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionBoth", "stripTimeMs": 0.0152}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionSourceOnly", "stripTimeMs": 0.014100000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionTargetOnly", "stripTimeMs": 0.016}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionBoth", "stripTimeMs": 0.0152}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionSourceOnly", "stripTimeMs": 0.014}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionTargetOnly", "stripTimeMs": 0.015000000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionBothDebug", "stripTimeMs": 0.0151}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionSourceOnlyDebug", "stripTimeMs": 0.014100000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionTargetOnlyDebug", "stripTimeMs": 0.013900000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionBothDebug", "stripTimeMs": 0.014}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionSourceOnlyDebug", "stripTimeMs": 0.0148}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionTargetOnlyDebug", "stripTimeMs": 0.0148}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionBothDebug", "stripTimeMs": 0.0148}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionSourceOnlyDebug", "stripTimeMs": 0.0137}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionTargetOnlyDebug", "stripTimeMs": 0.0159}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsTracing", "stripTimeMs": 0.014400000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsReprojection", "stripTimeMs": 0.0159}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionBoth", "stripTimeMs": 0.014700000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionSourceOnly", "stripTimeMs": 0.015600000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionTargetOnly", "stripTimeMs": 0.014}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionBoth", "stripTimeMs": 0.014}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionSourceOnly", "stripTimeMs": 0.014}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionTargetOnly", "stripTimeMs": 0.015600000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionBoth", "stripTimeMs": 0.014}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionSourceOnly", "stripTimeMs": 0.0117}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionTargetOnly", "stripTimeMs": 0.011300000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionBothDebug", "stripTimeMs": 0.015700000000000002}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionSourceOnlyDebug", "stripTimeMs": 0.014}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionTargetOnlyDebug", "stripTimeMs": 0.0173}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionBothDebug", "stripTimeMs": 0.014}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionSourceOnlyDebug", "stripTimeMs": 0.015300000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionTargetOnlyDebug", "stripTimeMs": 0.0142}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionBothDebug", "stripTimeMs": 0.013900000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionSourceOnlyDebug", "stripTimeMs": 0.013900000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionTargetOnlyDebug", "stripTimeMs": 0.015600000000000001}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "DepthOfFieldMip", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainColor", "stripTimeMs": 0.0196}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainColorAlpha", "stripTimeMs": 0.005200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainCoC", "stripTimeMs": 0.0047}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainColorCopy", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainColorCopyAlpha", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainColor", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainColorAlpha", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainCoC", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainColorCopy", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainColorCopyAlpha", "stripTimeMs": 0.0022}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "DoFCombine", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: UpsampleFastTiles", "stripTimeMs": 0.030100000000000002}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: UpsampleFastTiles", "stripTimeMs": 0.011000000000000001}]}]}, {"inputVariants": 10, "outputVariants": 0, "name": "StageWorkQueue", "pipelines": [{"inputVariants": 10, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: WorkQueueBuildArgs", "stripTimeMs": 0.013000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: WorkQueueBuild", "stripTimeMs": 0.0016}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: WorkQueueActiveBins", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: WorkQueueActiveClusters", "stripTimeMs": 0.0013000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: BuildFineRasterArgs", "stripTimeMs": 0.0012000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: WorkQueueBuildArgs", "stripTimeMs": 0.0012000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: WorkQueueBuild", "stripTimeMs": 0.0012000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: WorkQueueActiveBins", "stripTimeMs": 0.0012000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: WorkQueueActiveClusters", "stripTimeMs": 0.0012000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: BuildFineRasterArgs", "stripTimeMs": 0.0012000000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "DoFApertureShape", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ComputeShape<PERSON>uffer", "stripTimeMs": 0.016800000000000002}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ComputeShape<PERSON>uffer", "stripTimeMs": 0.006}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "GPUSort", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "", "variants": [{"inputVariants": 5, "outputVariants": 5, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0263}, {"inputVariants": 5, "outputVariants": 5, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.012700000000000001}, {"inputVariants": 5, "outputVariants": 5, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0146}, {"inputVariants": 5, "outputVariants": 5, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.01}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "GenerateMaxZ", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ComputeMaxZ", "stripTimeMs": 0.022000000000000002}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ComputeFinalMask", "stripTimeMs": 0.0078000000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: DilateMask", "stripTimeMs": 0.0073}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ComputeMaxZ", "stripTimeMs": 0.007200000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ComputeFinalMask", "stripTimeMs": 0.009000000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: DilateMask", "stripTimeMs": 0.007500000000000001}]}]}, {"inputVariants": 24, "outputVariants": 24, "name": "Sort", "pipelines": [{"inputVariants": 24, "outputVariants": 24, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort128", "stripTimeMs": 0.0171}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort1024", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort1024_128", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort2048", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort2048_128", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort4096", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort4096_128", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicPrePass", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicPrePass4096_128", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicPrePass2048_128", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort128", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort1024", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort1024_128", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort2048", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort2048_128", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort4096", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort4096_128", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicPrePass", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicPrePass4096_128", "stripTimeMs": 0.006}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicPrePass2048_128", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0046}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0043}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "GroundIrradiancePrecomputation", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: main", "stripTimeMs": 0.016800000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: main", "stripTimeMs": 0.0033}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Accumulation", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0213}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0098}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "MotionBlurMotionVecPrep", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 6, "outputVariants": 6, "variantName": "Kernel: MotionVecPreppingCS", "stripTimeMs": 0.0292}, {"inputVariants": 6, "outputVariants": 6, "variantName": "Kernel: MotionVecPreppingCS", "stripTimeMs": 0.0154}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "VolumetricLightingFiltering", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: FilterVolumetricLighting", "stripTimeMs": 0.019}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: FilterVolumetricLighting", "stripTimeMs": 0.0098}]}]}, {"inputVariants": 8, "outputVariants": 4, "name": "EdgeAdaptiveSpatialUpsampling", "pipelines": [{"inputVariants": 8, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0356}, {"inputVariants": 4, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.033600000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "DebugHDRxyMapping", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KCIExyGen", "stripTimeMs": 0.023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KCIExyGen", "stripTimeMs": 0.0064}]}]}, {"inputVariants": 256, "outputVariants": 128, "name": "UberPost", "pipelines": [{"inputVariants": 256, "outputVariants": 128, "pipeline": "", "variants": [{"inputVariants": 128, "outputVariants": 64, "variantName": "Kernel: Uber", "stripTimeMs": 0.7204}, {"inputVariants": 128, "outputVariants": 64, "variantName": "Kernel: Uber", "stripTimeMs": 0.47050000000000003}]}]}, {"inputVariants": 48, "outputVariants": 48, "name": "DepthOfFieldPrefilter", "pipelines": [{"inputVariants": 48, "outputVariants": 48, "pipeline": "", "variants": [{"inputVariants": 24, "outputVariants": 24, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.113}, {"inputVariants": 24, "outputVariants": 24, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0884}]}]}, {"inputVariants": 4, "outputVariants": 0, "name": "DebugLightVolumes", "pipelines": [{"inputVariants": 4, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: LightVolumeGradient", "stripTimeMs": 0.0125}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: LightVolumeColors", "stripTimeMs": 0.006200000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: LightVolumeGradient", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: LightVolumeColors", "stripTimeMs": 0.0017000000000000001}]}]}, {"inputVariants": 72, "outputVariants": 72, "name": "InstanceOcclusionCullingKernels", "pipelines": [{"inputVariants": 72, "outputVariants": 72, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: ResetDrawArgs", "stripTimeMs": 0.056600000000000004}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: CopyInstances", "stripTimeMs": 0.0478}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: CullInstances", "stripTimeMs": 0.047}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: ResetDrawArgs", "stripTimeMs": 0.044000000000000004}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: CopyInstances", "stripTimeMs": 0.043300000000000005}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: CullInstances", "stripTimeMs": 0.042100000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "InstanceDataBufferCopyKernels", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainCopyInstances", "stripTimeMs": 0.0207}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainCopyInstances", "stripTimeMs": 0.005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "ClearBuffer2D", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearBuffer2DMain", "stripTimeMs": 0.018500000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearBuffer2DMain", "stripTimeMs": 0.005}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "EVSMBlur", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ConvertAndBlur", "stripTimeMs": 0.0201}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Blur", "stripTimeMs": 0.0059}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CopyMoments", "stripTimeMs": 0.0055000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ConvertAndBlur", "stripTimeMs": 0.0055000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Blur", "stripTimeMs": 0.0048000000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CopyMoments", "stripTimeMs": 0.0047}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "materialflags", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: MaterialFlagsGen", "stripTimeMs": 0.026000000000000002}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: MaterialFlagsGen", "stripTimeMs": 0.0128}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "cleardispatchindirect", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearDispatchIndirect", "stripTimeMs": 0.0088}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearDispatchIndirect", "stripTimeMs": 0.005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "InstanceWindDataUpdateKernels", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: WindDataCopyHistoryMain", "stripTimeMs": 0.0081}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: WindDataCopyHistoryMain", "stripTimeMs": 0.0044}]}]}, {"inputVariants": 20, "outputVariants": 0, "name": "StageShadingSetup", "pipelines": [{"inputVariants": 20, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearShadingSampleVisibilityBuffer", "stripTimeMs": 0.015300000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearShadingCompactionBuffer", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearShadingAtlas", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearHistogram", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: CopyHistoryToShadingAtlas", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: CreateCompactedShadingSamplesMapping", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ReplicateShadedSamplesToShadingAtlas", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: CalculateHistogram", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: CalculateHighestVisibleHistogramID", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: DiscardSamplesBasedOnHistogram", "stripTimeMs": 0.005200000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearShadingSampleVisibilityBuffer", "stripTimeMs": 0.002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearShadingCompactionBuffer", "stripTimeMs": 0.0018000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearShadingAtlas", "stripTimeMs": 0.0018000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearHistogram", "stripTimeMs": 0.0017000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: CopyHistoryToShadingAtlas", "stripTimeMs": 0.0016}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: CreateCompactedShadingSamplesMapping", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ReplicateShadedSamplesToShadingAtlas", "stripTimeMs": 0.002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: CalculateHistogram", "stripTimeMs": 0.0017000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: CalculateHighestVisibleHistogramID", "stripTimeMs": 0.0018000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: DiscardSamplesBasedOnHistogram", "stripTimeMs": 0.0016}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "JPEGMP4Compression", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.020200000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSMain", "stripTimeMs": 0.0043}]}]}]}