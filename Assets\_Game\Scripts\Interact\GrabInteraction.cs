using UnityEngine;
using System.Collections.Generic;
using KinematicCharacterController.FPS;

public class GrabInteraction : MonoBehaviour
{
    [<PERSON><PERSON>("Grab Settings")]
    [Tooltip("How fast grabbed objects move toward target position")]
    [SerializeField] private float grabMovementSpeed = 30f;
    [Tooltip("How fast grabbed objects rotate when using right-click")]
    [SerializeField] private float grabRotationSpeed = 200f;
    [Tooltip("How much distance changes when scrolling")]
    [SerializeField] private float scrollDistanceChange = 2f;
    [Tooltip("Minimum grab distance")]
    [SerializeField] private float minGrabDistance = 1f;
    [Tooltip("Maximum grab distance")]
    [SerializeField] private float maxGrabDistance = 2f;
    [Tooltip("Force applied when throwing objects")]
    [SerializeField] private float throwForce = 20f;
    [Tooltip("How long F must be held to switch to grab mode")]
    [SerializeField] private float grabHoldTime = 0.2f;
    [Tooltip("Maximum distance at which objects can be grabbed")]
    [SerializeField] private float maxGrabReachDistance = 5f;
    [<PERSON>lt<PERSON>("Axis to rotate around with mouse X input")]
    [SerializeField] private Vector3 rotateXAxis = new Vector3(0, 1, 0);
    [Tooltip("Axis to rotate around with mouse Y input")]
    [SerializeField] private Vector3 rotateYAxis = new Vector3(1, 0, 0);
    [Tooltip("Axis to rotate around with E/Q keys")]
    [SerializeField] private Vector3 rotateZAxis = new Vector3(0, 0, 1);
    [Tooltip("Rotation speed for Z-axis (E/Q keys)")]
    [SerializeField] private float zRotationSpeed = 200f;
    [Tooltip("Snap angle increment when holding shift")]
    [SerializeField] private float snapAngle = 45f;
    [Tooltip("Sensitivity for snapped rotation")]
    [SerializeField] private float snappedRotationSensitivity = 8f;
    [Tooltip("Speed of rotation lerping")]
    [SerializeField] private float rotationLerpSpeed = 15f;
    
    [Header("Safety Settings")]
    [Tooltip("Extra buffer distance to add based on object size")]
    [SerializeField] private float objectSizeBuffer = 0.5f;
    [Tooltip("Player collision radius for safety calculations")]
    [SerializeField] private float playerCollisionRadius = 0.5f;
    [Tooltip("Time after throwing before collision with player is re-enabled")]
    [SerializeField] private float throwCollisionIgnoreTime = 0.3f;
    
    [Header("References")]
    [Tooltip("Reference to player camera - assign in inspector")]
    [SerializeField] private Camera playerCameraRef;
    
    // FPS Camera reference for coordination
    private FPSCharacterCamera fpsCamera;
    private FPSPlayerManager playerManager;
    private ToolSelectionManager toolSelectionManager;
    private FPSCharacterController playerController;
    
    // Grab system state variables
    private bool isGrabbing = false;
    private GameObject grabbedObject;
    private Rigidbody grabbedRigidbody;
    private float currentGrabDistance;
    private Vector3 grabRotation;
    private Vector3 lastPosition;
    
    // Object size tracking for safe distances
    private float grabbedObjectRadius = 0f;
    private Bounds grabbedObjectBounds;
    private bool hasBounds = false;
    
    [Header("Heavy Pull Settings")]
    [Tooltip("Maximum mass (kg) that can be grabbed normally. Heavier objects use pull system")]
    [SerializeField] private float normalGrabMassThreshold = 100f;
    [Tooltip("Spring strength pulling the heavy object's grabbed point toward the target point")]
    [SerializeField] private float heavyPullSpring = 300f;
    [Tooltip("Damping applied against point velocity to stabilize the pull")]
    [SerializeField] private float heavyPullDamping = 20f;
    [Tooltip("Maximum pull force applied at the contact point")]
    [SerializeField] private float heavyMaxForce = 8000f;
    [Tooltip("Maximum allowed distance from the anchor to the target point (soft cap)")]
    [SerializeField] private float heavyMaxLeashDistance = 3.0f;
    [Tooltip("Reference mass used to scale slowdown effect with mass")]
    [SerializeField] private float heavyReferenceMass = 50f;
    [Tooltip("Player slow multiplier at max stretch for very light objects (mass ~ 0)")]
    [SerializeField] private float heavyMinMultiplierLight = 0.6f;
    [Tooltip("Player slow multiplier at max stretch for heavy objects (mass >= reference)")]
    [SerializeField] private float heavyMinMultiplierHeavy = 0.2f;
    [Tooltip("Scale throw force based on object mass (lighter = throw farther)")]
    [SerializeField] private bool scaleThrowForceByMass = true;
    [Tooltip("Maximum throw force multiplier for very light objects")]
    [SerializeField] private float lightObjectThrowMultiplier = 2f;
    
    // Original values to restore
    private float originalDrag;
    private float originalAngularDrag;
    private bool originalUseGravity;
    private RigidbodyInterpolation originalInterpolation;
    private CollisionDetectionMode originalCollisionMode;
    
    // Layer management
    private int originalLayer;
    private int grabbedObjectLayer = 31; // Layer 31 for grabbed objects
    private int playerLayer;
    
    // Stack for iterative layer setting
    private Stack<Transform> layerSetStack = new Stack<Transform>(32);
    
    // Camera reference
    private Camera playerCamera => playerCameraRef != null ? playerCameraRef : Camera.main;
    
    // Original camera state
    private bool wasZooming = false;
    private Vector3 originalCameraRotation;
    private bool cameraControlsDisabled = false;
    
    // Rotation state
    private Quaternion desiredRotation = Quaternion.identity;
    private Vector3 lockedRotation = Vector3.zero;
    private bool isSnapping = false;
    private Vector3 forward;
    private Vector3 up;
    private Vector3 right;
    
    // Camera-relative rotation state
    private Quaternion initialRotationOffset = Quaternion.identity;
    private bool hasCustomRotation = false;
    
    // Public properties
    public bool IsGrabbing => isGrabbing;
    public float GrabHoldTime => grabHoldTime;
    public bool IsHeavyPulling => isHeavyPulling;
    
    // Heavy-object specific state
    private bool isHeavyPulling = false;
    private HeavyObjectManager heavyObjectManager;
    private Vector3 heavyLocalAnchor;
    private bool isLightHeavyObject = false;
    
    private void Awake()
    {
        playerLayer = LayerMask.NameToLayer("Player");
        if (playerLayer == -1)
        {
            Debug.LogWarning("[GrabInteraction] 'Player' layer not found. Physics interactions may not work correctly.");
        }
        else
        {
            Debug.Log($"[GrabInteraction] Found Player layer at index {playerLayer}");
        }
        
        Debug.Log($"[GrabInteraction] Using layer {grabbedObjectLayer} for grabbed objects");
        
        if (playerCameraRef == null)
        {
            playerCameraRef = Camera.main;
            Debug.Log($"[GrabInteraction] Using Camera.main as default");
        }
        
        if (fpsCamera == null)
        {
            if (playerCameraRef != null)
            {
                fpsCamera = playerCameraRef.GetComponent<FPSCharacterCamera>();
            }
            
            if (fpsCamera == null)
            {
                fpsCamera = FindObjectOfType<FPSCharacterCamera>();
            }
            
            if (fpsCamera == null)
            {
                Debug.LogError("[GrabInteraction] Could not find FPSCharacterCamera component!");
            }
            else
            {
                Debug.Log("[GrabInteraction] Found FPSCharacterCamera component");
            }
        }
        
        playerManager = FindObjectOfType<FPSPlayerManager>();
        if (playerManager == null)
        {
            Debug.LogWarning("[GrabInteraction] FPSPlayerManager not found");
        }
        
        toolSelectionManager = FindObjectOfType<ToolSelectionManager>();
        if (toolSelectionManager == null)
        {
            Debug.LogWarning("[GrabInteraction] ToolSelectionManager not found");
        }
    }
    
    public void Initialize(Camera camera)
    {
        if (camera != null)
        {
            playerCameraRef = camera;
            Debug.Log($"[GrabInteraction] Initialized with camera {camera.name}");
            
            if (fpsCamera == null)
            {
                fpsCamera = camera.GetComponent<FPSCharacterCamera>();
                if (fpsCamera == null)
                {
                    Debug.LogError("[GrabInteraction] FPSCharacterCamera component not found on camera");
                }
            }
        }
    }
    
    private void OnEnable()
    {
        if (fpsCamera == null)
        {
            fpsCamera = FindObjectOfType<FPSCharacterCamera>();
            if (fpsCamera == null)
            {
                Debug.LogError("[GrabInteraction] Could not find FPSCharacterCamera component!");
            }
        }
        
        if (playerController == null)
        {
            playerController = FindObjectOfType<FPSCharacterController>();
        }
    }
    
    private void Update()
    {
        if (isGrabbing)
        {
            UpdateGrabbedObject();
            
            if (Input.GetMouseButtonDown(0))
            {
                ReleaseGrabbedObject(true); // Throw
            }
            
            if (Input.GetKeyUp(KeyCode.F))
            {
                ReleaseGrabbedObject(false); // Just drop
            }
        }
        else if (isHeavyPulling)
        {
            UpdateHeavyPull();
            
            if (Input.GetMouseButtonDown(0))
            {
                ReleaseGrabbedObject(true); // Throw
            }
            
            if (Input.GetKeyUp(KeyCode.F))
            {
                ReleaseGrabbedObject(false); // Release
            }
        }
    }
    
    /// <summary>
    /// Calculates the safe minimum distance for grabbing based on object size
    /// </summary>
    private float CalculateSafeGrabDistance(GameObject obj, float baseDistance)
    {
        // Try to get bounds from colliders
        Collider[] colliders = obj.GetComponentsInChildren<Collider>();
        if (colliders.Length > 0)
        {
            Bounds combinedBounds = colliders[0].bounds;
            for (int i = 1; i < colliders.Length; i++)
            {
                if (colliders[i].enabled && !colliders[i].isTrigger)
                {
                    combinedBounds.Encapsulate(colliders[i].bounds);
                }
            }
            
            grabbedObjectBounds = combinedBounds;
            hasBounds = true;
            
            // Calculate the radius of the object from its bounds
            // Use the maximum extent in any single axis instead of diagonal magnitude for less restrictive calculation
            grabbedObjectRadius = Mathf.Max(combinedBounds.extents.x, combinedBounds.extents.y, combinedBounds.extents.z);
            
            // Calculate minimum safe distance: object radius + player radius + reduced buffer
            // Use a smaller multiplier for the object radius to allow closer grabbing
            float minSafeDistance = (grabbedObjectRadius * 0.7f) + playerCollisionRadius + (objectSizeBuffer * 0.5f);
            
            Debug.Log($"[GrabInteraction] Object radius: {grabbedObjectRadius}, Min safe distance: {minSafeDistance}");
            
            // Return the greater of the base distance or the calculated safe distance
            return Mathf.Max(baseDistance, minSafeDistance);
        }
        
        // Fallback if no colliders found
        grabbedObjectRadius = 0.5f; // Assume a default radius
        return Mathf.Max(baseDistance, playerCollisionRadius + objectSizeBuffer);
    }
    
    /// <summary>
    /// Attempts to grab an object the player is looking at
    /// </summary>
    public void AttemptGrab()
    {
        if (playerCamera == null)
        {
            Debug.LogError("[GrabInteraction] Player camera is null. Cannot grab.");
            return;
        }
        
        Debug.Log($"[GrabInteraction] Attempting to grab with camera {playerCamera.name}");
        
        Ray ray = new Ray(playerCamera.transform.position, playerCamera.transform.forward);
        RaycastHit hitInfo;

        int layerMask = ~(1 << playerLayer);
        if (Physics.Raycast(ray, out hitInfo, maxGrabReachDistance, layerMask))
        {
            Debug.Log($"[GrabInteraction] Raycast hit: {hitInfo.collider.gameObject.name}");
            
            Rigidbody rb = hitInfo.collider.GetComponent<Rigidbody>();
            if (rb == null)
            {
                rb = hitInfo.collider.GetComponentInParent<Rigidbody>();
            }
            
            if (rb != null)
            {
                Debug.Log($"[GrabInteraction] Found rigidbody: {rb.gameObject.name}");
                
                grabbedObject = rb.gameObject;
                grabbedRigidbody = rb;
                
                heavyObjectManager = rb.GetComponentInParent<HeavyObjectManager>();
                
                bool useNormalGrab = true;
                
                if (heavyObjectManager != null)
                {
                    if (heavyObjectManager.IsLightEnoughForNormalGrab)
                    {
                        Debug.Log($"[GrabInteraction] Heavy object with mass {heavyObjectManager.Mass}kg - using normal grab");
                        isLightHeavyObject = true;
                        useNormalGrab = true;
                    }
                    else
                    {
                        Debug.Log($"[GrabInteraction] Heavy object with mass {heavyObjectManager.Mass}kg - using heavy pull");
                        useNormalGrab = false;
                    }
                }
                
                IInteractable interactable = grabbedObject.GetComponent<IInteractable>();
                if (interactable != null)
                {
                    interactable.RemoveHighlight();
                }
                
                originalDrag = grabbedRigidbody.linearDamping;
                originalAngularDrag = grabbedRigidbody.angularDamping;
                originalUseGravity = grabbedRigidbody.useGravity;
                originalInterpolation = grabbedRigidbody.interpolation;
                originalCollisionMode = grabbedRigidbody.collisionDetectionMode;
                originalLayer = grabbedObject.layer;
                
                if (useNormalGrab)
                {
                    // Calculate safe minimum distance based on object size
                    float safeMinDistance = CalculateSafeGrabDistance(grabbedObject, minGrabDistance);
                    currentGrabDistance = safeMinDistance;
                    
                    grabRotation = grabbedObject.transform.rotation.eulerAngles;
                    lastPosition = grabbedObject.transform.position;
                    
                    initialRotationOffset = Quaternion.Inverse(playerCamera.transform.rotation) * grabbedObject.transform.rotation;
                    hasCustomRotation = false;
                    
                    grabbedRigidbody.interpolation = RigidbodyInterpolation.Interpolate;
                    grabbedRigidbody.collisionDetectionMode = CollisionDetectionMode.ContinuousDynamic;
                    grabbedRigidbody.useGravity = false;
                    // Reduce damping for more responsive movement
                    grabbedRigidbody.linearDamping = 0f;
                    grabbedRigidbody.angularDamping = 4f;
                    // Freeze velocity to prevent physics interference with MovePosition
                    grabbedRigidbody.linearVelocity = Vector3.zero;
                    grabbedRigidbody.angularVelocity = Vector3.zero;
                    
                    SetLayerIteratively(grabbedObject, grabbedObjectLayer);
                    Debug.Log($"[GrabInteraction] Changed {grabbedObject.name} to layer {grabbedObjectLayer}");
                    
                    if (toolSelectionManager != null)
                    {
                        toolSelectionManager.SetScrollEnabled(false);
                        Debug.Log("[GrabInteraction] Disabled tool selection scrolling");
                    }
                    
                    isGrabbing = true;
                    isHeavyPulling = false;
                    
                    if (fpsCamera != null)
                    {
                        wasZooming = fpsCamera.IsCurrentlyZooming;
                        
                        if (wasZooming)
                        {
                            fpsCamera.SetPauseState(true);
                            fpsCamera.SetPauseState(false);
                        }
                    }
                    
                    Debug.Log($"[GrabInteraction] Started grabbing {grabbedObject.name} at safe distance: {currentGrabDistance}");
                }
                else
                {
                    // Heavy pull for truly heavy objects
                    heavyLocalAnchor = rb.transform.InverseTransformPoint(hitInfo.point);
                    
                    // Calculate safe distance for heavy objects too
                    float safeDistance = CalculateSafeGrabDistance(grabbedObject, hitInfo.distance);
                    currentGrabDistance = Mathf.Clamp(safeDistance, minGrabDistance, maxGrabDistance);
                    
                    if (toolSelectionManager != null)
                    {
                        toolSelectionManager.SetScrollEnabled(false);
                    }
                    
                    isHeavyPulling = true;
                    isGrabbing = false;
                    isLightHeavyObject = false;
                    
                    if (playerController != null)
                    {
                        playerController.SetExternalSpeedMultiplier(0.6f);
                    }
                    Debug.Log($"[GrabInteraction] Heavy pull started on {grabbedObject.name} at safe distance: {currentGrabDistance}");
                }
            }
            else
            {
                Debug.Log($"[GrabInteraction] No rigidbody found on {hitInfo.collider.gameObject.name}");
            }
        }
        else
        {
            Debug.Log($"[GrabInteraction] Raycast hit nothing");
        }
    }
    
    private void SetLayerIteratively(GameObject obj, int layer)
    {
        layerSetStack.Clear();
        layerSetStack.Push(obj.transform);
        
        while (layerSetStack.Count > 0)
        {
            Transform current = layerSetStack.Pop();
            current.gameObject.layer = layer;
            
            foreach (Transform child in current)
            {
                layerSetStack.Push(child);
            }
        }
    }
    
    private void SetLayerRecursively(GameObject obj, int layer)
    {
        obj.layer = layer;
        
        foreach (Transform child in obj.transform)
        {
            SetLayerRecursively(child.gameObject, layer);
        }
    }
    
    /// <summary>
    /// Updates the position and rotation of the grabbed object
    /// </summary>
    private void UpdateGrabbedObject()
    {
        if (grabbedObject == null || grabbedRigidbody == null)
        {
            isGrabbing = false;
            return;
        }
        
        // Check for scroll wheel to adjust distance - FLIPPED DIRECTION
        float scrollInput = Input.GetAxis("Mouse ScrollWheel");
        if (scrollInput != 0)
        {
            // Calculate new distance
            float newDistance = currentGrabDistance + (scrollInput * scrollDistanceChange);
            
            // Ensure we maintain minimum safe distance based on object size
            float safeMinDistance = (grabbedObjectRadius * 0.7f) + playerCollisionRadius + (objectSizeBuffer * 0.5f);
            newDistance = Mathf.Clamp(newDistance, Mathf.Max(minGrabDistance, safeMinDistance), maxGrabDistance);
            
            currentGrabDistance = newDistance;
            
            Debug.Log($"[GrabInteraction] Adjusted grab distance to: {currentGrabDistance} (safe min: {safeMinDistance})");
        }
        
        // Calculate target position
        Vector3 targetPosition = playerCamera.transform.position + playerCamera.transform.forward * currentGrabDistance;
        
        // Ensure the object doesn't get too close to the player
        Vector3 toObject = targetPosition - playerCamera.transform.position;
        float actualDistance = toObject.magnitude;
        float minSafeDistance = (grabbedObjectRadius * 0.7f) + playerCollisionRadius + (objectSizeBuffer * 0.5f);
        
        if (actualDistance < minSafeDistance)
        {
            targetPosition = playerCamera.transform.position + toObject.normalized * minSafeDistance;
        }
        
        // Use MovePosition for immediate, smooth following without lag
        // This eliminates the velocity-based lag when moving fast
        grabbedRigidbody.MovePosition(Vector3.Lerp(
            grabbedObject.transform.position,
            targetPosition,
            Time.fixedDeltaTime * grabMovementSpeed
        ));
        
        // Clear any accumulated velocity to prevent drift and ensure clean movement
        grabbedRigidbody.linearVelocity = Vector3.zero;
        
        lastPosition = grabbedObject.transform.position;
        
        bool isRotatingObject = Input.GetMouseButton(1);
        
        if (isRotatingObject)
        {
            hasCustomRotation = true;
            
            if (!cameraControlsDisabled)
            {
                if (fpsCamera == null)
                {
                    fpsCamera = FindObjectOfType<FPSCharacterCamera>();
                }
                
                if (fpsCamera != null)
                {
                    originalCameraRotation = fpsCamera.transform.eulerAngles;
                    desiredRotation = grabbedRigidbody.rotation;
                    
                    fpsCamera.SetGrabRotationMode(true);
                    
                    cameraControlsDisabled = true;
                    Debug.Log("[GrabInteraction] Entered grab rotation mode");
                }
                else
                {
                    Debug.LogError("[GrabInteraction] Cannot enter grab rotation mode - no camera reference!");
                }
            }
            
            float mouseX = Input.GetAxis("Mouse X");
            float mouseY = Input.GetAxis("Mouse Y");
            
            isSnapping = Input.GetKey(KeyCode.LeftShift) || Input.GetKey(KeyCode.RightShift);
            
            UpdateRotationAxes();
            
            if (isSnapping)
            {
                HandleSnapRotation(mouseX, mouseY);
            }
            else if (Mathf.Abs(mouseX) > 0.1f || Mathf.Abs(mouseY) > 0.1f)
            {
                Quaternion rotationX = Quaternion.AngleAxis(-mouseX * grabRotationSpeed * Time.deltaTime, up);
                Quaternion rotationY = Quaternion.AngleAxis(mouseY * grabRotationSpeed * Time.deltaTime, right);
                
                desiredRotation = rotationY * rotationX * desiredRotation;
            }
            
            if (Input.GetKey(KeyCode.E))
            {
                Quaternion rotationZ = Quaternion.AngleAxis(zRotationSpeed * Time.deltaTime, forward);
                desiredRotation = rotationZ * desiredRotation;
            }
            else if (Input.GetKey(KeyCode.Q))
            {
                Quaternion rotationZ = Quaternion.AngleAxis(-zRotationSpeed * Time.deltaTime, forward);
                desiredRotation = rotationZ * desiredRotation;
            }
            
            grabbedRigidbody.MoveRotation(Quaternion.Lerp(
                grabbedRigidbody.rotation,
                desiredRotation,
                Time.deltaTime * rotationLerpSpeed
            ));
        }
        else if (cameraControlsDisabled)
        {
            if (fpsCamera != null)
            {
                fpsCamera.SetGrabRotationMode(false);
            }
            
            cameraControlsDisabled = false;
            Debug.Log("[GrabInteraction] Exited grab rotation mode");
        }
        else if (!hasCustomRotation)
        {
            Quaternion targetRotation = playerCamera.transform.rotation * initialRotationOffset;
            grabbedRigidbody.MoveRotation(Quaternion.Lerp(
                grabbedRigidbody.rotation,
                targetRotation,
                Time.deltaTime * rotationLerpSpeed
            ));
        }
    }
    
    /// <summary>
    /// Applies a spring-like pull at the stored anchor point for heavy objects
    /// </summary>
    private void UpdateHeavyPull()
    {
        if (grabbedObject == null || grabbedRigidbody == null)
        {
            isHeavyPulling = false;
            if (playerController != null)
            {
                playerController.SetExternalSpeedMultiplier(1f);
            }
            return;
        }
        
        float scrollInput = Input.GetAxis("Mouse ScrollWheel");
        if (scrollInput != 0)
        {
            // Calculate new distance with safety checks
            float newDistance = currentGrabDistance + (scrollInput * scrollDistanceChange);
            float safeMinDistance = (grabbedObjectRadius * 0.7f) + playerCollisionRadius + (objectSizeBuffer * 0.5f);
            newDistance = Mathf.Clamp(newDistance, Mathf.Max(minGrabDistance, safeMinDistance), maxGrabDistance);
            currentGrabDistance = newDistance;
        }
        
        Vector3 targetPoint = playerCamera.transform.position + playerCamera.transform.forward * currentGrabDistance;
        Vector3 anchorWorld = grabbedRigidbody.transform.TransformPoint(heavyLocalAnchor);
        
        // Apply extra safety check for heavy objects
        Vector3 toTarget = targetPoint - playerCamera.transform.position;
        float actualDistance = toTarget.magnitude;
        float minSafeDistance = (grabbedObjectRadius * 0.7f) + playerCollisionRadius + (objectSizeBuffer * 0.5f);
        
        if (actualDistance < minSafeDistance)
        {
            targetPoint = playerCamera.transform.position + toTarget.normalized * minSafeDistance;
        }
        
        Vector3 toDesired = targetPoint - anchorWorld;
        float toDesiredMag = toDesired.magnitude;
        if (toDesiredMag > heavyMaxLeashDistance && toDesiredMag > 0.0001f)
        {
            targetPoint = anchorWorld + toDesired * (heavyMaxLeashDistance / toDesiredMag);
        }

        Vector3 displacement = targetPoint - anchorWorld;
        Vector3 pointVelocity = grabbedRigidbody.GetPointVelocity(anchorWorld);
        Vector3 force = (displacement * heavyPullSpring) - (pointVelocity * heavyPullDamping);
        
        if (force.sqrMagnitude > heavyMaxForce * heavyMaxForce)
        {
            force = force.normalized * heavyMaxForce;
        }
        
        grabbedRigidbody.AddForceAtPosition(force, anchorWorld, ForceMode.Force);
        
        if (playerController != null)
        {
            float stretch = Mathf.Clamp01(displacement.magnitude / Mathf.Max(heavyMaxLeashDistance, 0.001f));
            float massNorm = Mathf.Clamp01(grabbedRigidbody != null && heavyReferenceMass > 0f ? grabbedRigidbody.mass / heavyReferenceMass : 0f);
            float minAtMaxStretch = Mathf.Lerp(heavyMinMultiplierLight, heavyMinMultiplierHeavy, massNorm);
            float multiplier = Mathf.Lerp(1f, minAtMaxStretch, stretch);
            playerController.SetExternalSpeedMultiplier(multiplier);
        }
    }
    
    private void UpdateRotationAxes()
    {
        if (!isSnapping)
        {
            forward = playerCamera.transform.forward;
            right = playerCamera.transform.right;
            up = playerCamera.transform.up;
            return;
        }
        
        var directions = new List<Vector3>
        {
            grabbedObject.transform.forward,
            -grabbedObject.transform.forward,
            grabbedObject.transform.up,
            -grabbedObject.transform.up,
            grabbedObject.transform.right,
            -grabbedObject.transform.right
        };
        
        up = GetClosestDirection(directions, playerCamera.transform.up);
        directions.Remove(up);
        directions.Remove(-up);
        
        forward = GetClosestDirection(directions, playerCamera.transform.forward);
        directions.Remove(forward);
        directions.Remove(-forward);
        
        right = GetClosestDirection(directions, playerCamera.transform.right);
    }
    
    private Vector3 GetClosestDirection(List<Vector3> directions, Vector3 reference)
    {
        float maxDot = -Mathf.Infinity;
        Vector3 closest = Vector3.zero;
        
        foreach (Vector3 dir in directions)
        {
            float dot = Vector3.Dot(reference, dir);
            if (dot > maxDot)
            {
                maxDot = dot;
                closest = dir;
            }
        }
        
        return closest;
    }
    
    private void HandleSnapRotation(float mouseX, float mouseY)
    {
        lockedRotation += new Vector3(-mouseX, mouseY, 0) * grabRotationSpeed * Time.deltaTime;
        
        if (Mathf.Abs(lockedRotation.x) > snappedRotationSensitivity || 
            Mathf.Abs(lockedRotation.y) > snappedRotationSensitivity)
        {
            Quaternion snapRotation = Quaternion.identity;
            
            if (Mathf.Abs(lockedRotation.x) > Mathf.Abs(lockedRotation.y))
            {
                float angle = lockedRotation.x > 0 ? snapAngle : -snapAngle;
                snapRotation = Quaternion.AngleAxis(angle, up);
            }
            else
            {
                float angle = lockedRotation.y > 0 ? snapAngle : -snapAngle;
                snapRotation = Quaternion.AngleAxis(angle, right);
            }
            
            desiredRotation = snapRotation * desiredRotation;
            
            lockedRotation = Vector3.zero;
            
            Vector3 snappedEuler = desiredRotation.eulerAngles;
            snappedEuler.x = Mathf.Round(snappedEuler.x / snapAngle) * snapAngle;
            snappedEuler.y = Mathf.Round(snappedEuler.y / snapAngle) * snapAngle;
            snappedEuler.z = Mathf.Round(snappedEuler.z / snapAngle) * snapAngle;
            desiredRotation = Quaternion.Euler(snappedEuler);
        }
    }
    
    /// <summary>
    /// Releases the currently grabbed object
    /// </summary>
    /// <param name="throwObject">Whether to throw the object or just release it</param>
    public void ReleaseGrabbedObject(bool throwObject)
    {
        if (grabbedObject == null || grabbedRigidbody == null)
        {
            isGrabbing = false;
            isHeavyPulling = false;
            return;
        }
        
        Debug.Log($"[GrabInteraction] Releasing {grabbedObject.name}, throwing: {throwObject}");
        
        if (fpsCamera != null && cameraControlsDisabled)
        {
            fpsCamera.SetGrabRotationMode(false);
            cameraControlsDisabled = false;
        }
        
        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;
        
        SetLayerIteratively(grabbedObject, originalLayer);
        Debug.Log($"[GrabInteraction] Restored {grabbedObject.name} to original layer {originalLayer}");
        
        if (originalInterpolation != grabbedRigidbody.interpolation)
            grabbedRigidbody.interpolation = originalInterpolation;
        if (originalCollisionMode != grabbedRigidbody.collisionDetectionMode)
            grabbedRigidbody.collisionDetectionMode = originalCollisionMode;
        grabbedRigidbody.linearDamping = originalDrag;
        grabbedRigidbody.angularDamping = originalAngularDrag;
        grabbedRigidbody.useGravity = originalUseGravity;
        
        if (throwObject)
        {
            // Temporarily ignore collision between player and thrown object
            if (heavyObjectManager != null && playerLayer >= 0)
            {
                StartCoroutine(TemporarilyIgnoreCollision(grabbedObject, throwCollisionIgnoreTime));
            }
            
            float finalThrowForce = throwForce;
            
            if (scaleThrowForceByMass && grabbedRigidbody != null)
            {
                float mass = grabbedRigidbody.mass;
                
                if (mass < 10f)
                {
                    float lightMultiplier = Mathf.Lerp(lightObjectThrowMultiplier, 1f, mass / 10f);
                    finalThrowForce *= lightMultiplier;
                }
                else if (mass > normalGrabMassThreshold)
                {
                    float heavyMultiplier = Mathf.Lerp(1f, 0.3f, (mass - normalGrabMassThreshold) / normalGrabMassThreshold);
                    finalThrowForce *= heavyMultiplier;
                }
            }
            
            // Apply throw force with a slight forward offset to ensure clean separation
            Vector3 throwDirection = playerCamera.transform.forward;
            
            // For heavy objects, ensure we're throwing from a safe position
            if (isHeavyPulling)
            {
                Vector3 anchorWorld = grabbedRigidbody.transform.TransformPoint(heavyLocalAnchor);
                grabbedRigidbody.AddForceAtPosition(throwDirection * finalThrowForce, anchorWorld, ForceMode.Impulse);
            }
            else
            {
                // Add a small position offset in throw direction to ensure separation
                grabbedObject.transform.position += throwDirection * 0.1f;
                grabbedRigidbody.linearVelocity = throwDirection * finalThrowForce;
            }
            
            Debug.Log($"[GrabInteraction] Throwing with force: {finalThrowForce} (base: {throwForce}, mass: {grabbedRigidbody.mass}kg)");
        }
        
        InvItemPickup itemPickup = grabbedObject.GetComponent<InvItemPickup>();
        if (itemPickup != null)
        {
            // Removed SetTimeSinceThrown - no longer needed for 3D game
        }
        
        if (toolSelectionManager != null)
        {
            toolSelectionManager.SetScrollEnabled(true);
            Debug.Log("[GrabInteraction] Re-enabled tool selection scrolling");
        }
        
        // Reset all grab state
        grabbedObject = null;
        grabbedRigidbody = null;
        isGrabbing = false;
        isHeavyPulling = false;
        isLightHeavyObject = false;
        heavyObjectManager = null;
        grabbedObjectRadius = 0f;
        hasBounds = false;
        
        if (playerController != null)
        {
            playerController.SetExternalSpeedMultiplier(1f);
        }
        
        initialRotationOffset = Quaternion.identity;
        hasCustomRotation = false;
    }
    
    /// <summary>
    /// Temporarily ignores collision between an object and the player
    /// </summary>
    private System.Collections.IEnumerator TemporarilyIgnoreCollision(GameObject obj, float duration)
    {
        // Get all colliders on the thrown object
        Collider[] objectColliders = obj.GetComponentsInChildren<Collider>();
        
        // Get player colliders
        GameObject player = GameObject.FindGameObjectWithTag("Player");
        if (player == null) yield break;
        
        Collider[] playerColliders = player.GetComponentsInChildren<Collider>();
        
        // Ignore collision between all pairs
        foreach (Collider objCol in objectColliders)
        {
            foreach (Collider playerCol in playerColliders)
            {
                if (objCol != null && playerCol != null)
                {
                    Physics.IgnoreCollision(objCol, playerCol, true);
                }
            }
        }
        
        Debug.Log($"[GrabInteraction] Ignoring collision with player for {duration} seconds");
        
        // Wait for the specified duration
        yield return new WaitForSeconds(duration);
        
        // Re-enable collision
        foreach (Collider objCol in objectColliders)
        {
            foreach (Collider playerCol in playerColliders)
            {
                if (objCol != null && playerCol != null)
                {
                    Physics.IgnoreCollision(objCol, playerCol, false);
                }
            }
        }
        
        Debug.Log("[GrabInteraction] Re-enabled collision with player");
    }
}