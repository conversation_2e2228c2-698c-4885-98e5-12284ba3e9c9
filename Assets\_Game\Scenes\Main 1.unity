%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 20
    smallestHole: 5
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 10
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 13
  m_BakeOnSceneLoad: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 512
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 256
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 1
    m_PVRDenoiserTypeDirect: 1
    m_PVRDenoiserTypeIndirect: 1
    m_PVRDenoiserTypeAO: 1
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 112000000, guid: f42ea30a6c43fbf44b01bf37d169a110, type: 2}
  m_LightingSettings: {fileID: 4890085278179872738, guid: b56cfb6f5cd74a54482a9647084213a9, type: 2}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 3
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    buildHeightMesh: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &165177947
GameObject:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 165177949}
  - component: {fileID: 165177948}
  m_Layer: 0
  m_Name: UModelerXEditableMeshCache_88e682f1-444b-4bdf-8674-7fd0b4710acc
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &165177948
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 165177947}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 233418904}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!4 &165177949
Transform:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 165177947}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!115 &233418904
MonoScript:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 7
  m_DefaultReferences: {}
  m_Icon: {fileID: 0}
  m_Type: 0
  m_ExecutionOrder: 0
  m_ClassName: UModelerXEditableMeshCache
  m_Namespace: Tripolygon.UModelerX.Runtime
  m_AssemblyName: Tripolygon.UModelerX.Runtime
--- !u!115 &774092136
MonoScript:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  serializedVersion: 7
  m_DefaultReferences: {}
  m_Icon: {fileID: 0}
  m_Type: 0
  m_ExecutionOrder: 0
  m_ClassName: UMXCommandServiceCache
  m_Namespace: Tripolygon.UModelerX.Runtime
  m_AssemblyName: Tripolygon.UModelerX.Runtime
--- !u!1 &1045504573
GameObject:
  m_ObjectHideFlags: 19
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1045504575}
  m_Layer: 0
  m_Name: SceneIDMap
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1045504575
Transform:
  m_ObjectHideFlags: 19
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1045504573}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1336208362
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ec0b4dd729a12d046982652f834580a2, type: 3}
  m_Name: Main 1_LM0
  m_EditorClassIdentifier: 
  resolution: 2048
  bitmask: 1
  id: 0
  sortingID: 0
  isImplicit: 1
  area: 192174.58
  totalVertexCount: 0
  vertexCounter: 0
  sceneLodLevel: -1
  autoResolution: 0
  sceneName: 
  tag: -1
  containsTerrains: 0
  probes: 0
  mode: 1
  renderMode: 1000
  renderDirMode: 1000
  atlasPacker: 1000
  holeFilling: 0
  computeSSS: 0
  sssSamples: 16
  sssDensity: 10
  sssColor: {r: 1, g: 1, b: 1, a: 1}
  sssScale: 1
  fakeShadowBias: 0
  transparentSelfShadow: 0
  flipNormal: 0
  parentName: 
  overridePath: 
  fixPos3D: 0
  voxelSize: {x: 1, y: 1, z: 1}
  passedFilter: 0
--- !u!1 &1426247360
GameObject:
  m_ObjectHideFlags: 17
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1426247362}
  m_Layer: 0
  m_Name: '[default-CSGModel]'
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 124
  m_IsActive: 0
--- !u!4 &1426247362
Transform:
  m_ObjectHideFlags: 17
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1426247360}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: -1.371, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2101801065}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1522427159
GameObject:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1522427161}
  - component: {fileID: 1522427160}
  m_Layer: 0
  m_Name: StaticLightingSky
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1522427160
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1522427159}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 441482e8936e35048a1dffac814e3ef8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Profile: {fileID: 11400000, guid: e21ba01aa32ab4d4299b42d6bb7ad35d, type: 2}
  m_StaticLightingSkyUniqueID: 0
  m_StaticLightingCloudsUniqueID: 0
  m_StaticLightingVolumetricClouds: 0
  bounces: 1
--- !u!4 &1522427161
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1522427159}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1686833045
GameObject:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1686833047}
  - component: {fileID: 1686833046}
  m_Layer: 0
  m_Name: UMXCommandServiceCache_d2f45678-5cd4-4bb8-af1d-7f273335060b
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1686833046
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1686833045}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 774092136}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!4 &1686833047
Transform:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1686833045}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1764792763
GameObject:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1764792765}
  - component: {fileID: 1764792764}
  m_Layer: 0
  m_Name: ProbeVolumePerSceneData
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1764792764
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1764792763}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a83d2f7ae04ab6f4f99b0d85377be998, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializedBakingSet: {fileID: 11400000, guid: d443d8c83a5097043a6afeaf6350aeb7, type: 2}
  sceneGUID: cb05773a888b6df449e8111c4ef7b930
  obsoleteAsset: {fileID: 0}
  obsoleteCellSharedDataAsset: {fileID: 0}
  obsoleteCellSupportDataAsset: {fileID: 0}
  obsoleteSerializedScenarios: []
--- !u!4 &1764792765
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1764792763}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1779442231 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 919132149155446097, guid: 36ad3872d8d474545ae31ff89494253b, type: 3}
  m_PrefabInstance: {fileID: 2086189308}
  m_PrefabAsset: {fileID: 0}
--- !u!64 &1779442232
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1779442231}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 5
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: -3537709356157935800, guid: 36ad3872d8d474545ae31ff89494253b, type: 3}
--- !u!1 &1893677603
GameObject:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1893677605}
  - component: {fileID: 1893677604}
  m_Layer: 0
  m_Name: '!ftraceLightmaps'
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1893677604
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1893677603}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b7fa80e7116296f4eb4f49ec1544ee22, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  renderSettingsBounces: 5
  renderSettingsGISamples: 64
  renderSettingsGIBackFaceWeight: 0
  renderSettingsTileSize: 1024
  renderSettingsPriority: 3
  renderSettingsTexelsPerUnit: 5
  renderSettingsForceRefresh: 1
  renderSettingsForceRebuildGeometry: 1
  renderSettingsPerformRendering: 1
  renderSettingsUserRenderMode: 0
  renderSettingsDistanceShadowmask: 1
  renderSettingsSettingsMode: 0
  renderSettingsFixSeams: 1
  renderSettingsDenoise: 1
  renderSettingsDenoise2x: 0
  renderSettingsEncode: 1
  renderSettingsEncodeMode: 0
  renderSettingsOverwriteWarning: 0
  renderSettingsAutoAtlas: 1
  renderSettingsUnwrapUVs: 1
  renderSettingsForceDisableUnwrapUVs: 0
  renderSettingsMaxAutoResolution: 4096
  renderSettingsMinAutoResolution: 16
  renderSettingsUnloadScenes: 1
  renderSettingsAdjustSamples: 1
  renderSettingsGILODMode: 2
  renderSettingsGILODModeEnabled: 0
  renderSettingsCheckOverlaps: 0
  renderSettingsSkipOutOfBoundsUVs: 1
  renderSettingsHackEmissiveBoost: 1
  renderSettingsHackIndirectBoost: 1
  renderSettingsTempPath: 
  renderSettingsOutPath: BakeryLightmaps
  renderSettingsUseScenePath: 0
  renderSettingsHackAOIntensity: 0
  renderSettingsHackAOSamples: 16
  renderSettingsHackAORadius: 1
  renderSettingsShowAOSettings: 0
  renderSettingsShowTasks: 1
  renderSettingsShowTasks2: 0
  renderSettingsShowPaths: 1
  renderSettingsShowNet: 1
  renderSettingsOcclusionProbes: 0
  renderSettingsTexelsPerMap: 0
  renderSettingsTexelsColor: 1
  renderSettingsTexelsMask: 1
  renderSettingsTexelsDir: 1
  renderSettingsShowDirWarning: 1
  renderSettingsRenderDirMode: 0
  renderSettingsShowCheckerSettings: 0
  renderSettingsSamplesWarning: 1
  renderSettingsSuppressPopups: 0
  renderSettingsPrefabWarning: 1
  renderSettingsSplitByScene: 0
  renderSettingsSplitByTag: 0
  renderSettingsUVPaddingMax: 0
  renderSettingsPostPacking: 1
  renderSettingsHoleFilling: 0
  renderSettingsBeepOnFinish: 0
  renderSettingsExportTerrainAsHeightmap: 0
  renderSettingsRTXMode: 1
  renderSettingsLightProbeMode: 1
  renderSettingsClientMode: 0
  renderSettingsServerAddress: 127.0.0.1
  renderSettingsUnwrapper: 0
  renderSettingsDenoiserType: 101
  renderSettingsExportTerrainTrees: 0
  renderSettingsShowPerf: 1
  renderSettingsSampleDiv: 1
  renderSettingsAtlasPacker: 0
  renderSettingsBatchPoints: 1
  renderSettingsCompressVolumes: 0
  renderSettingsBatchAreaLightSampleLimit: 0
  renderSettingsSector: {fileID: 0}
  renderSettingsRTPVExport: 1
  renderSettingsRTPVSceneView: 0
  renderSettingsRTPVHDR: 0
  renderSettingsRTPVWidth: 640
  renderSettingsRTPVHeight: 360
  lastBakeTime: 25
  enlightenWarningShown: 0
  enlightenWarningShown2: 0
  uniqueLights:
  - {fileID: 0}
  - {fileID: 0}
  lights:
  - tform:
      e00: 1
      e01: 0
      e02: 0
      e03: 355.92
      e10: 0
      e11: 1
      e12: 0
      e13: 600.08
      e20: 0
      e21: 0
      e22: 1
      e23: 1592.4
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    color: {r: 0.91502756, g: 0, b: 0.44095612, a: 1}
    intensity: 74.47108
    range: 61.129547
    radius: 0.05
    samples: 8
    samples2: 0
    bitmask: 0
    bakeToIndirect: 0
    selfShadow: 0
    realisticFalloff: 1
    projMode: 0
    cookie: {fileID: 0}
    angle: 30
    UID: 1
  - tform:
      e00: 1
      e01: 0
      e02: 0
      e03: 335.54
      e10: 0
      e11: 1
      e12: 0
      e13: 615.24
      e20: 0
      e21: 0
      e22: 1
      e23: 1568.59
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    color: {r: 0.91502756, g: 0, b: 0.44095612, a: 1}
    intensity: 1013.2118
    range: 61.129547
    radius: 0.05
    samples: 8
    samples2: 0
    bitmask: 0
    bakeToIndirect: 0
    selfShadow: 0
    realisticFalloff: 1
    projMode: 0
    cookie: {fileID: 0}
    angle: 30
    UID: 2
  implicitGroups:
  - {fileID: 1336208362}
  implicitGroupedObjects:
  - {fileID: 1779442231}
  bounds:
  - m_Center: {x: 354.50754, y: 599.08496, z: 1588.4742}
    m_Extent: {x: 13.95346, y: 14.609543, z: 21.126408}
  hasEmissive: 01
  uvBuffOffsets: 
  uvBuffLengths: 
  uvSrcBuff: []
  uvDestBuff: []
  lmrIndicesOffsets: 
  lmrIndicesLengths: 
  lmrIndicesBuff: 
  lmGroupLODResFlags: 
  lmGroupMinLOD: 
  lmGroupLODMatrix: 
  serverGetFileList: []
  lightmapHasColor: 01
  lightmapHasMask: 00000000
  lightmapHasDir: 00
  lightmapHasRNM: 00
  modifiedAssetPathList:
  - Assets/_Game/Models/Area-1/Cube_015.fbx
  modifiedAssets:
  - meshName:
    - Cube.015
    padding: 01000000
    unwrapper: 00000000
  maps:
  - {fileID: 2800000, guid: 1df1c26aa7a7a7b42a36e04dd31eb4c3, type: 3}
  masks: []
  dirMaps: []
  rnmMaps0: []
  rnmMaps1: []
  rnmMaps2: []
  mapsMode: 
  bakedRenderers:
  - {fileID: 2086189309}
  bakedIDs: 00000000
  bakedScaleOffset:
  - {x: 0.9977895, y: 1.0010228, z: 0.0014648438, w: 0.0014648438}
  bakedVertexOffset: ffffffff
  bakedVertexColorMesh:
  - {fileID: 0}
  nonBakedRenderers: []
  bakedLights:
  - {fileID: 0}
  - {fileID: 0}
  bakedLightChannels: ffffffffffffffff
  bakedRenderersTerrain: []
  bakedIDsTerrain: 
  bakedScaleOffsetTerrain: []
  assetList: []
  uvOverlapAssetList: 
  idremap: 2b000000
  usesRealtimeGI: 0
  emptyDirectionTex: {fileID: 0}
  anyVolumes: 0
  compressedVolumes: 0
  sectors:
  - name: $G
    maps:
    - {fileID: 2800000, guid: 1df1c26aa7a7a7b42a36e04dd31eb4c3, type: 3}
    masks: []
    dirMaps: []
    rnmMaps0: []
    rnmMaps1: []
    rnmMaps2: []
    mapsMode: 
    bakedRenderers:
    - {fileID: 2086189309}
    bakedIDs: 00000000
    bakedScaleOffset:
    - {x: 0.9992554, y: 1.0024935, z: 0.0007324219, w: 0.0007324219}
    bakedVertexColorMesh:
    - {fileID: 0}
    bakedRenderersTerrain: []
    bakedIDsTerrain: 
    bakedScaleOffsetTerrain: []
    nonBakedRenderers: []
  prevBakedProbes: []
  prevBakedProbePos: []
--- !u!4 &1893677605
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1893677603}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &2086189308
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: -8679921383154817045, guid: 36ad3872d8d474545ae31ff89494253b, type: 3}
      propertyPath: m_LocalPosition.x
      value: 364.75
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 36ad3872d8d474545ae31ff89494253b, type: 3}
      propertyPath: m_LocalPosition.y
      value: 599.85
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 36ad3872d8d474545ae31ff89494253b, type: 3}
      propertyPath: m_LocalPosition.z
      value: 1586.89
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 36ad3872d8d474545ae31ff89494253b, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.70710653
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 36ad3872d8d474545ae31ff89494253b, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.70710653
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 36ad3872d8d474545ae31ff89494253b, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.00063038646
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 36ad3872d8d474545ae31ff89494253b, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0.00063038635
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 36ad3872d8d474545ae31ff89494253b, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 36ad3872d8d474545ae31ff89494253b, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 36ad3872d8d474545ae31ff89494253b, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -7511558181221131132, guid: 36ad3872d8d474545ae31ff89494253b, type: 3}
      propertyPath: m_ScaleInLightmap
      value: 15
      objectReference: {fileID: 0}
    - target: {fileID: -7511558181221131132, guid: 36ad3872d8d474545ae31ff89494253b, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: e1873b9b4da5585489cd1042dcf68b55, type: 2}
    - target: {fileID: 919132149155446097, guid: 36ad3872d8d474545ae31ff89494253b, type: 3}
      propertyPath: m_Name
      value: Cube_015
      objectReference: {fileID: 0}
    - target: {fileID: 919132149155446097, guid: 36ad3872d8d474545ae31ff89494253b, type: 3}
      propertyPath: m_StaticEditorFlags
      value: 2147483647
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 919132149155446097, guid: 36ad3872d8d474545ae31ff89494253b, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1779442232}
  m_SourcePrefab: {fileID: 100100000, guid: 36ad3872d8d474545ae31ff89494253b, type: 3}
--- !u!23 &2086189309 stripped
MeshRenderer:
  m_CorrespondingSourceObject: {fileID: -7511558181221131132, guid: 36ad3872d8d474545ae31ff89494253b, type: 3}
  m_PrefabInstance: {fileID: 2086189308}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &2101801064
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2101801065}
  m_Layer: 0
  m_Name: '[generated-meshes]'
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2101801065
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2101801064}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1426247362}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1660057539 &9223372036854775807
SceneRoots:
  m_ObjectHideFlags: 0
  m_Roots:
  - {fileID: 1045504575}
  - {fileID: 1522427161}
  - {fileID: 1426247362}
  - {fileID: 165177949}
  - {fileID: 1686833047}
  - {fileID: 1764792765}
  - {fileID: 2086189308}
  - {fileID: 1893677605}
